"""
Quick Medical Record Search

A simple script to quickly search medical records without interactive mode.
Perfect for testing and integration with other systems.
"""

import os
import sys
from pathlib import Path

# Add the current directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from medical_record_embedder import MedicalRecordEmbedder
from config import *

def quick_search(query: str, limit: int = 5, min_score: float = 0.3):
    """
    Perform a quick search of medical records
    
    Args:
        query: Search query
        limit: Maximum number of results
        min_score: Minimum similarity score
    
    Returns:
        List of search results
    """
    print(f"🔍 Searching for: '{query}'")
    
    # Initialize embedder
    embedder = MedicalRecordEmbedder(model_name=EMBEDDING_MODEL)
    
    try:
        # Setup Qdrant connection
        embedder.setup_qdrant(host=QDRANT_HOST, port=QDRANT_PORT)
        
        # Search
        results = embedder.query_similar_records(query, limit=limit)
        
        # Filter by score
        filtered_results = [r for r in results if r['score'] >= min_score]
        
        return filtered_results
        
    except Exception as e:
        print(f"❌ Error: {e}")
        print("Make sure Qdrant is running and data is processed")
        return []

def display_results(results, query):
    """Display search results"""
    if not results:
        print("❌ No relevant results found")
        return
    
    print(f"\n📋 Found {len(results)} relevant records:")
    print("=" * 60)
    
    for i, result in enumerate(results, 1):
        record = result['record']
        score = result['score']
        filename = Path(record['file_path']).name
        
        print(f"\n{i}. [{score:.3f}] {filename}")
        print(f"   Patient: {record['patient_id']}")
        print(f"   Type: {record['record_type'].capitalize()}")
        print(f"   Diagnosis: {record['primary_diagnosis']}")
        
        if record['medications']:
            meds = ', '.join(record['medications'][:2])
            if len(record['medications']) > 2:
                meds += f" (+{len(record['medications'])-2} more)"
            print(f"   Medications: {meds}")
        
        # Show snippet of medical content
        content = record['specialized_content'][:150]
        if len(record['specialized_content']) > 150:
            content += "..."
        print(f"   Content: {content}")

def main():
    """Main function for quick search"""
    
    # Pre-defined test queries if no arguments provided
    test_queries = [
        "patient with heart problems and chest pain",
        "teenage depression and suicide attempt",
        "abdominal pain and appendicitis",
        "diabetes patient with high blood sugar",
        "stroke patient with neurological problems",
        "elderly patient with breathing difficulties"
    ]
    
    if len(sys.argv) > 1:
        # Use command line argument as query
        query = ' '.join(sys.argv[1:])
        results = quick_search(query)
        display_results(results, query)
    else:
        # Run test queries
        print("🏥 QUICK MEDICAL RECORD SEARCH TEST")
        print("=" * 50)
        print("Running test queries...\n")
        
        for i, query in enumerate(test_queries, 1):
            print(f"\n{'='*60}")
            print(f"TEST {i}/{len(test_queries)}")
            print(f"{'='*60}")
            
            results = quick_search(query, limit=3)
            display_results(results, query)
        
        print(f"\n{'='*60}")
        print("✅ Test completed!")
        print("\nUsage examples:")
        print("python quick_search.py 'heart attack patient'")
        print("python quick_search.py 'diabetes medication'")
        print("python quick_search.py 'teenage suicide'")

if __name__ == "__main__":
    main()
