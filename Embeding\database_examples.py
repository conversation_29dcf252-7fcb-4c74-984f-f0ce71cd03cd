"""
Simple Database Reset and Update Examples

This script shows different ways to reset and update your vector database.
"""

from medical_record_embedder import MedicalRecordEmbedder
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)

def example_1_quick_reset():
    """Example 1: Quick reset and update"""
    print("=== Example 1: Quick Reset and Update ===")
    
    # Initialize embedder
    embedder = MedicalRecordEmbedder(enable_chunking=True, max_chunk_size=300)
    
    # Setup Qdrant connection
    embedder.setup_qdrant()
    
    # Show current stats
    print("Current database stats:")
    stats = embedder.get_collection_stats()
    print(f"  - Exists: {stats['exists']}")
    print(f"  - Count: {stats.get('count', 0)}")
    print(f"  - Chunking: {stats.get('chunking_enabled', False)}")
    
    # Update database (clear and reload)
    embedder.update_database(reset_method="clear")
    
    # Show final stats
    print("\nFinal database stats:")
    final_stats = embedder.get_collection_stats()
    print(f"  - Count: {final_stats['count']}")
    print(f"  - Chunk distribution: {final_stats.get('chunk_distribution', {})}")

def example_2_manual_reset():
    """Example 2: Manual step-by-step reset"""
    print("\n=== Example 2: Manual Step-by-Step Reset ===")
    
    # Initialize embedder
    embedder = MedicalRecordEmbedder(enable_chunking=True, max_chunk_size=300)
    embedder.setup_qdrant()
    
    # Step 1: Clear existing data
    print("Step 1: Clearing existing data...")
    embedder.clear_collection(confirm=True)
    
    # Step 2: Process new data
    print("Step 2: Processing dataset...")
    records = embedder.process_dataset("d:/Chatbot_hospital/dataset")
    
    # Step 3: Create embeddings
    print("Step 3: Creating embeddings...")
    embeddings = embedder.create_embeddings(records)
    
    # Step 4: Store in database
    print("Step 4: Storing embeddings...")
    embedder.store_in_qdrant(embeddings)
    
    # Step 5: Verify
    print("Step 5: Verification...")
    stats = embedder.get_collection_stats()
    print(f"Total embeddings stored: {stats['count']}")

def example_3_recreate_collection():
    """Example 3: Completely recreate the collection"""
    print("\n=== Example 3: Recreate Collection ===")
    
    embedder = MedicalRecordEmbedder(enable_chunking=True, max_chunk_size=300)
    embedder.setup_qdrant()
    
    # Reset (delete and recreate collection)
    embedder.update_database(reset_method="reset")
    
    print("Collection recreated and updated!")

def example_4_check_stats_only():
    """Example 4: Just check current database statistics"""
    print("\n=== Example 4: Database Statistics ===")
    
    embedder = MedicalRecordEmbedder()
    embedder.setup_qdrant()
    
    stats = embedder.get_collection_stats()
    
    if stats['exists']:
        print(f"Collection exists: YES")
        print(f"Total points: {stats['count']}")
        print(f"Vector size: {stats['vector_size']}")
        print(f"Distance metric: {stats['distance_metric']}")
        print(f"Chunking enabled: {stats['chunking_enabled']}")
        
        if stats['chunk_distribution']:
            print("Chunk distribution:")
            for chunk_type, count in stats['chunk_distribution'].items():
                print(f"  - {chunk_type}: {count}")
    else:
        print("Collection does not exist")

def example_5_test_search_after_update():
    """Example 5: Test search functionality after update"""
    print("\n=== Example 5: Test Search After Update ===")
    
    embedder = MedicalRecordEmbedder(enable_chunking=True, max_chunk_size=300)
    embedder.setup_qdrant()
    
    # Quick update
    embedder.update_database(reset_method="clear")
    
    # Test different types of searches
    queries = [
        "heart problems and chest pain",
        "diabetes and blood sugar",
        "psychiatric evaluation",
        "medication and treatment"
    ]
    
    for query in queries:
        print(f"\nQuery: '{query}'")
        results = embedder.query_similar_records(query, limit=3)
        
        for i, result in enumerate(results, 1):
            file_name = result['file_path'].split('\\')[-1]  # Get filename only
            print(f"  {i}. {result['chunk_type']} - {result['summary']} - {file_name} (Score: {result['score']:.3f})")

if __name__ == "__main__":
    print("Choose an example to run:")
    print("1. Quick reset and update")
    print("2. Manual step-by-step reset")
    print("3. Recreate collection")
    print("4. Check database statistics")
    print("5. Test search after update")
    
    choice = input("\nEnter choice (1-5): ").strip()
    
    if choice == "1":
        example_1_quick_reset()
    elif choice == "2":
        example_2_manual_reset()
    elif choice == "3":
        example_3_recreate_collection()
    elif choice == "4":
        example_4_check_stats_only()
    elif choice == "5":
        example_5_test_search_after_update()
    else:
        print("Invalid choice. Running example 1...")
        example_1_quick_reset()
