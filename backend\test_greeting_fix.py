#!/usr/bin/env python3
"""
Test script to verify the greeting functionality fix.
"""

import asyncio
import json
import sys
import os

# Add the backend directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from config import Config
from app.core import setup_vanna
from app.utils import MemoryCache

async def test_greeting():
    """Test the greeting functionality"""
    print("🧪 Testing greeting functionality...")
    
    # Setup Vanna
    print("📝 Setting up Vanna...")
    vn = setup_vanna()
    
    if not vn:
        print("❌ Failed to setup Vanna")
        return False
    
    # Test greeting
    test_question = "Hi, what can you do?"
    print(f"❓ Testing question: '{test_question}'")
    
    try:
        # Generate response
        print("🤖 Generating response...")
        response_stream = vn.generate_interactive_response(
            question=test_question,
            current_id="test_greeting_001",
            allow_llm_to_see_data=True
        )
        
        # Collect the response
        response_chunks = []
        for chunk in response_stream:
            if chunk.startswith("data: "):
                try:
                    data = json.loads(chunk[6:])  # Remove "data: " prefix
                    if data.get('type') == 'chunk':
                        response_chunks.append(data.get('content', ''))
                    elif data.get('type') == 'chat_result':
                        response_chunks.append(data.get('message', ''))
                        break
                except json.JSONDecodeError:
                    continue
        
        final_response = ''.join(response_chunks).strip()
        
        print(f"🤖 Response: '{final_response}'")
        
        # Check if response is appropriate for greeting
        expected_keywords = ['hello', 'help', 'patient', 'doctor', 'info', 'consultations']
        unwanted_keywords = ['sql', 'postgresql', 'query', 'database', 'table']
        
        response_lower = final_response.lower()
        
        has_expected = any(keyword in response_lower for keyword in expected_keywords)
        has_unwanted = any(keyword in response_lower for keyword in unwanted_keywords)
        
        print(f"✅ Has expected keywords: {has_expected}")
        print(f"❌ Has unwanted technical keywords: {has_unwanted}")
        
        # Test result
        if has_expected and not has_unwanted and len(final_response) < 200:
            print("🎉 TEST PASSED: Greeting response is appropriate!")
            return True
        else:
            print("❌ TEST FAILED: Greeting response is not appropriate")
            if not has_expected:
                print("   - Missing expected friendly keywords")
            if has_unwanted:
                print("   - Contains unwanted technical keywords")
            if len(final_response) >= 200:
                print("   - Response is too long for a greeting")
            return False
            
    except Exception as e:
        print(f"❌ Error during test: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 Starting greeting test...")
    result = asyncio.run(test_greeting())
    
    if result:
        print("\n✅ Greeting test completed successfully!")
        sys.exit(0)
    else:
        print("\n❌ Greeting test failed!")
        sys.exit(1)
