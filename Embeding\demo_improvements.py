"""
Demonstration Script for Improved Medical Record Embedding

This script demonstrates the improvements in embedding quality and
shows how to query the medical records database effectively.
"""

import os
import sys
import json
from pathlib import Path
from typing import List, Dict
import pandas as pd

# Add the current directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from medical_record_embedder import MedicalRecordEmbedder
from config import *

class MedicalRecordDemo:
    """Demonstration class for medical record embedding improvements"""
    
    def __init__(self):
        self.embedder = MedicalRecordEmbedder(model_name=EMBEDDING_MODEL)
        self.dataset_path = DATASET_PATH
        
    def demonstrate_content_extraction(self, sample_files: List[str] = None):
        """
        Demonstrate how content extraction removes redundant information
        
        Args:
            sample_files: List of specific files to demonstrate with
        """
        print("=== CONTENT EXTRACTION DEMONSTRATION ===\n")
        
        if not sample_files:
            # Use first few files as samples
            sample_files = [
                "data_1.md",
                "Medical_Report_1.md", 
                "Sample medical report 1.md",
                "document_1_legrand.md"
            ]
        
        for filename in sample_files:
            filepath = Path(self.dataset_path) / filename
            if not filepath.exists():
                print(f"⚠️  File not found: {filename}")
                continue
                
            print(f"📄 File: {filename}")
            print("-" * 50)
            
            # Read original content
            with open(filepath, 'r', encoding='utf-8') as f:
                original_content = f.read()
            
            # Extract specialized content
            specialized_content = self.embedder.extract_medical_content(original_content)
            
            print(f"Original length: {len(original_content)} characters")
            print(f"Extracted length: {len(specialized_content)} characters")
            print(f"Reduction: {100 * (1 - len(specialized_content)/len(original_content)):.1f}%")
            
            print("\n🔍 Extracted Medical Content:")
            print(specialized_content[:300] + "..." if len(specialized_content) > 300 else specialized_content)
            print("\n" + "="*70 + "\n")
    
    def demonstrate_record_parsing(self, sample_files: List[str] = None):
        """
        Demonstrate structured record parsing
        
        Args:
            sample_files: List of specific files to demonstrate with
        """
        print("=== STRUCTURED RECORD PARSING DEMONSTRATION ===\n")
        
        if not sample_files:
            sample_files = ["data_1.md", "document_1_legrand.md", "sample1.md"]
        
        parsed_records = []
        
        for filename in sample_files:
            filepath = Path(self.dataset_path) / filename
            if not filepath.exists():
                continue
                
            record = self.embedder.parse_medical_record(str(filepath))
            if record:
                parsed_records.append(record)
                
                print(f"📋 Record from: {filename}")
                print("-" * 50)
                print(f"Patient ID: {record.patient_id}")
                print(f"Record Type: {record.record_type}")
                print(f"Department: {record.department}")
                print(f"Primary Diagnosis: {record.primary_diagnosis}")
                print(f"Medications: {', '.join(record.medications[:3])}{'...' if len(record.medications) > 3 else ''}")
                print(f"Allergies: {', '.join(record.allergies) if record.allergies else 'None'}")
                print(f"Physician: {record.physician}")
                print(f"Admission Date: {record.admission_date}")
                print("\n" + "="*70 + "\n")
        
        return parsed_records
    
    def demonstrate_deduplication(self):
        """Demonstrate deduplication process"""
        print("=== DEDUPLICATION DEMONSTRATION ===\n")
        
        # Process a subset of files
        sample_files = list(Path(self.dataset_path).glob("*.md"))[:20]  # First 20 files
        
        records = []
        for file_path in sample_files:
            record = self.embedder.parse_medical_record(str(file_path))
            if record:
                records.append(record)
        
        print(f"📊 Before deduplication: {len(records)} records")
        
        # Group by content hash to show potential duplicates
        hash_groups = {}
        for record in records:
            hash_key = record.content_hash
            if hash_key not in hash_groups:
                hash_groups[hash_key] = []
            hash_groups[hash_key].append(record)
        
        duplicate_groups = {k: v for k, v in hash_groups.items() if len(v) > 1}
        
        if duplicate_groups:
            print(f"🔍 Found {len(duplicate_groups)} groups of potential duplicates:")
            for hash_key, group in duplicate_groups.items():
                print(f"  - Hash {hash_key[:8]}...: {len(group)} files")
                for record in group:
                    print(f"    • {Path(record.file_path).name}")
        else:
            print("✅ No exact duplicates found in sample")
        
        # Run deduplication
        unique_records = self.embedder.deduplicate_records(records)
        print(f"📊 After deduplication: {len(unique_records)} unique records")
        print(f"🗑️  Removed: {len(records) - len(unique_records)} duplicates")
        print("\n" + "="*70 + "\n")
        
        return unique_records
    
    def demonstrate_specialty_classification(self):
        """Demonstrate medical specialty classification"""
        print("=== SPECIALTY CLASSIFICATION DEMONSTRATION ===\n")
        
        # Process all records
        records = self.embedder.process_dataset(self.dataset_path)
        
        # Group by specialty
        specialty_groups = {}
        for record in records:
            specialty = record.record_type
            if specialty not in specialty_groups:
                specialty_groups[specialty] = []
            specialty_groups[specialty].append(record)
        
        print(f"📊 Specialty Distribution (Total: {len(records)} records):")
        print("-" * 50)
        
        for specialty, group in sorted(specialty_groups.items(), key=lambda x: len(x[1]), reverse=True):
            percentage = 100 * len(group) / len(records)
            print(f"{specialty.capitalize():15} | {len(group):3d} records ({percentage:4.1f}%)")
            
            # Show sample diagnoses for this specialty
            sample_diagnoses = list(set([r.primary_diagnosis for r in group[:5] if r.primary_diagnosis]))
            if sample_diagnoses:
                print(f"                  Sample diagnoses: {', '.join(sample_diagnoses[:2])}")
        
        print("\n" + "="*70 + "\n")
        
        return specialty_groups
    
    def demonstrate_query_system(self, records: List = None):
        """
        Demonstrate the query system with improved embeddings
        
        Args:
            records: Pre-processed records to use
        """
        print("=== QUERY SYSTEM DEMONSTRATION ===\n")
        
        # Setup Qdrant and embeddings if not already done
        if not hasattr(self, '_embeddings_ready'):
            print("🔧 Setting up embeddings...")
            if not records:
                records = self.embedder.process_dataset(self.dataset_path)
            
            embeddings = self.embedder.create_embeddings(records)
            
            try:
                self.embedder.setup_qdrant(host=QDRANT_HOST, port=QDRANT_PORT)
                self.embedder.store_in_qdrant(embeddings)
                self._embeddings_ready = True
                print("✅ Embeddings ready!")
            except Exception as e:
                print(f"❌ Error setting up Qdrant: {e}")
                print("Please make sure Qdrant is running (use setup_qdrant.py)")
                return
        
        # Test queries
        test_queries = [
            "patient with heart attack and chest pain",
            "teenage suicide attempt with medication overdose",
            "abdominal pain and appendicitis surgery",
            "diabetes patient with high blood sugar",
            "stroke patient with neurological deficit",
            "elderly patient with breathing problems"
        ]
        
        print("🔍 Testing query system with medical scenarios:")
        print("-" * 50)
        
        for i, query in enumerate(test_queries, 1):
            print(f"\n{i}. Query: '{query}'")
            try:
                results = self.embedder.query_similar_records(query, limit=3)
                print("   Results:")
                for j, result in enumerate(results, 1):
                    score = result['score']
                    summary = result['summary']
                    filename = Path(result['record']['file_path']).name
                    
                    print(f"     {j}. {score:.3f} | {summary} | {filename}")
            except Exception as e:
                print(f"   ❌ Error: {e}")
        
        print("\n" + "="*70 + "\n")
    
    def generate_comparison_report(self):
        """Generate a comparison report showing improvements"""
        print("=== IMPROVEMENT COMPARISON REPORT ===\n")
        
        # Analyze the dataset
        dataset_path = Path(self.dataset_path)
        md_files = list(dataset_path.glob("*.md"))
        
        # Calculate metrics
        total_files = len(md_files)
        total_size = sum(f.stat().st_size for f in md_files)
        
        # Process records
        records = self.embedder.process_dataset(self.dataset_path)
        unique_records = len(records)
        
        # Calculate content reduction
        original_content_size = 0
        extracted_content_size = 0
        
        for record in records[:10]:  # Sample for efficiency
            with open(record.file_path, 'r', encoding='utf-8') as f:
                original = f.read()
            original_content_size += len(original)
            extracted_content_size += len(record.specialized_content)
        
        if original_content_size > 0:
            reduction_percentage = 100 * (1 - extracted_content_size / original_content_size)
        else:
            reduction_percentage = 0
        
        print("📊 Dataset Statistics:")
        print(f"   Total files: {total_files}")
        print(f"   Total size: {total_size / 1024:.1f} KB")
        print(f"   Unique records after processing: {unique_records}")
        print(f"   Deduplication rate: {100 * (1 - unique_records / total_files):.1f}%")
        print(f"   Content reduction (sample): {reduction_percentage:.1f}%")
        
        print("\n🎯 Key Improvements:")
        print("   ✅ Removed redundant administrative information")
        print("   ✅ Extracted specialized medical content")
        print("   ✅ Classified records by medical specialty")
        print("   ✅ Implemented content-based deduplication")
        print("   ✅ Structured data extraction (diagnoses, medications, etc.)")
        print("   ✅ Improved embedding quality for better search results")
        
        print("\n" + "="*70 + "\n")

def main():
    """Main demonstration function"""
    demo = MedicalRecordDemo()
    
    print("🏥 HOSPITAL MEDICAL RECORD EMBEDDING DEMONSTRATION")
    print("=" * 70)
    
    # Step 1: Content extraction
    demo.demonstrate_content_extraction()
    
    # Step 2: Record parsing
    parsed_records = demo.demonstrate_record_parsing()
    
    # Step 3: Deduplication
    unique_records = demo.demonstrate_deduplication()
    
    # Step 4: Specialty classification
    specialty_groups = demo.demonstrate_specialty_classification()
    
    # Step 5: Query system
    demo.demonstrate_query_system()
    
    # Step 6: Comparison report
    demo.generate_comparison_report()
    
    print("🎉 Demonstration completed!")
    print("Next steps:")
    print("1. Run 'python setup_qdrant.py' to set up Qdrant")
    print("2. Run 'python process_hospital_data.py' to process your full dataset")
    print("3. Integrate with your chatbot system")

if __name__ == "__main__":
    main()
