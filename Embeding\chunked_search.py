"""
Advanced Search with Chunked Medical Records

This script provides advanced search capabilities using the new chunking feature,
allowing for more precise and targeted searches within medical records.
"""

import os
import sys
from pathlib import Path
from medical_record_embedder import MedicalRecordEmbedder
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ChunkedMedicalSearch:
    """Advanced search interface for chunked medical records"""
    
    def __init__(self, dataset_path: str = "d:/Chatbot_hospital/dataset"):
        self.dataset_path = dataset_path
        self.embedder = MedicalRecordEmbedder(enable_chunking=True, max_chunk_size=300)
        self.is_initialized = False
        
    def initialize(self):
        """Initialize the search system"""
        try:
            # Setup Qdrant
            self.embedder.setup_qdrant()
            
            # Process dataset
            print("Processing medical records...")
            records = self.embedder.process_dataset(self.dataset_path)
            print(f"Processed {len(records)} records")
            
            # Create and store embeddings
            print("Creating chunked embeddings...")
            embeddings = self.embedder.create_embeddings(records)
            print(f"Created {len(embeddings)} chunk embeddings")
            
            # Store in Qdrant
            print("Storing embeddings in vector database...")
            self.embedder.store_in_qdrant(embeddings)
            
            self.is_initialized = True
            print("✓ Search system initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize search system: {e}")
            raise
    
    def search_by_category(self, query: str, category: str = None, limit: int = 10):
        """
        Search for medical records with optional category filtering
        
        Args:
            query: Search query
            category: Optional category filter (diagnosis, medications, procedures, etc.)
            limit: Maximum number of results
        """
        if not self.is_initialized:
            raise ValueError("Search system not initialized. Call initialize() first.")
        
        results = self.embedder.query_similar_records(query, limit=limit)
        
        # Filter by category if specified
        if category:
            results = [r for r in results if r.get('chunk_type') == category]
        
        return results
    
    def search_diagnosis(self, query: str, limit: int = 5):
        """Search specifically in diagnosis-related chunks"""
        return self.search_by_category(query, category='diagnosis', limit=limit)
    
    def search_medications(self, query: str, limit: int = 5):
        """Search specifically in medication-related chunks"""
        return self.search_by_category(query, category='medications', limit=limit)
    
    def search_procedures(self, query: str, limit: int = 5):
        """Search specifically in procedure-related chunks"""
        return self.search_by_category(query, category='procedures', limit=limit)
    
    def search_clinical_notes(self, query: str, limit: int = 5):
        """Search specifically in clinical examination chunks"""
        return self.search_by_category(query, category='clinical_notes', limit=limit)
    
    def search_patient_info(self, query: str, limit: int = 5):
        """Search specifically in patient header information"""
        return self.search_by_category(query, category='header', limit=limit)
    
    def comprehensive_search(self, query: str, limit: int = 10):
        """Perform comprehensive search across all chunk types"""
        results = self.search_by_category(query, limit=limit)
        
        # Group results by chunk type
        grouped_results = {}
        for result in results:
            chunk_type = result.get('chunk_type', 'unknown')
            if chunk_type not in grouped_results:
                grouped_results[chunk_type] = []
            grouped_results[chunk_type].append(result)
        
        return grouped_results
    
    def find_related_chunks(self, file_path: str, limit: int = 10):
        """Find all chunks related to a specific file"""
        # Get the file name for comparison
        file_name = Path(file_path).name
        
        # Search for chunks from this file
        results = self.embedder.query_similar_records(file_name, limit=limit)
        
        # Filter to only include chunks from this specific file
        related_chunks = [r for r in results if Path(r['file_path']).name == file_name]
        
        return related_chunks
    
    def print_search_results(self, results, title="Search Results"):
        """Pretty print search results"""
        print(f"\n=== {title} ===")
        
        if not results:
            print("No results found.")
            return
        
        if isinstance(results, dict):
            # Grouped results
            for chunk_type, chunk_results in results.items():
                print(f"\n{chunk_type.upper()} CHUNKS:")
                for result in chunk_results:
                    self._print_single_result(result)
        else:
            # Simple list of results
            for result in results:
                self._print_single_result(result)
    
    def _print_single_result(self, result):
        """Print a single search result"""
        file_name = Path(result['file_path']).name
        
        if result['type'] == 'chunk':
            print(f"  📄 {file_name}")
            print(f"    Patient: {result['patient_id']}")
            print(f"    Chunk Type: {result['chunk_type']}")
            print(f"    Score: {result['score']:.3f}")
            print(f"    Content: {result['content'][:150]}...")
            print(f"    Diagnosis: {result['primary_diagnosis']}")
            print()
        else:
            print(f"  📄 {file_name}")
            print(f"    Patient: {result['patient_id']}")
            print(f"    Type: Full Record")
            print(f"    Score: {result['score']:.3f}")
            print(f"    Summary: {result['summary']}")
            print()

def interactive_search():
    """Interactive search interface"""
    print("=== INTERACTIVE CHUNKED MEDICAL SEARCH ===\n")
    
    # Initialize search system
    search_system = ChunkedMedicalSearch()
    print("Initializing search system...")
    search_system.initialize()
    
    print("\nAvailable search types:")
    print("1. diagnosis - Search in diagnosis-related content")
    print("2. medications - Search in medication-related content") 
    print("3. procedures - Search in procedure-related content")
    print("4. clinical - Search in clinical examination notes")
    print("5. patient - Search in patient header information")
    print("6. comprehensive - Search across all types")
    print("7. quit - Exit the search interface")
    
    while True:
        print("\n" + "="*50)
        search_type = input("Enter search type (1-7): ").strip()
        
        if search_type == '7' or search_type.lower() == 'quit':
            print("Goodbye!")
            break
        
        if search_type not in ['1', '2', '3', '4', '5', '6']:
            print("Invalid search type. Please enter 1-7.")
            continue
        
        query = input("Enter your search query: ").strip()
        
        if not query:
            print("Empty query. Please enter a search term.")
            continue
        
        try:
            # Perform search based on type
            if search_type == '1':
                results = search_system.search_diagnosis(query)
                search_system.print_search_results(results, "Diagnosis Search Results")
            elif search_type == '2':
                results = search_system.search_medications(query)
                search_system.print_search_results(results, "Medication Search Results")
            elif search_type == '3':
                results = search_system.search_procedures(query)
                search_system.print_search_results(results, "Procedure Search Results")
            elif search_type == '4':
                results = search_system.search_clinical_notes(query)
                search_system.print_search_results(results, "Clinical Notes Search Results")
            elif search_type == '5':
                results = search_system.search_patient_info(query)
                search_system.print_search_results(results, "Patient Info Search Results")
            elif search_type == '6':
                results = search_system.comprehensive_search(query)
                search_system.print_search_results(results, "Comprehensive Search Results")
        
        except Exception as e:
            print(f"Error during search: {e}")
            logger.error(f"Search error: {e}")

def demo_targeted_searches():
    """Demonstrate targeted search capabilities"""
    print("=== TARGETED SEARCH DEMONSTRATION ===\n")
    
    # Initialize search system
    search_system = ChunkedMedicalSearch()
    search_system.initialize()
    
    # Demo searches
    searches = [
        ("Cardiac patients", "diagnosis", "patient with heart problems"),
        ("Pain medications", "medications", "patient taking pain medication"),
        ("Emergency procedures", "procedures", "emergency surgical procedure"),
        ("Vital signs", "clinical", "blood pressure and heart rate"),
        ("Department admissions", "patient", "emergency department admission")
    ]
    
    for title, search_type, query in searches:
        print(f"\n{title}:")
        print(f"Query: '{query}' (searching in {search_type})")
        
        if search_type == "diagnosis":
            results = search_system.search_diagnosis(query)
        elif search_type == "medications":
            results = search_system.search_medications(query)
        elif search_type == "procedures":
            results = search_system.search_procedures(query)
        elif search_type == "clinical":
            results = search_system.search_clinical_notes(query)
        elif search_type == "patient":
            results = search_system.search_patient_info(query)
        
        if results:
            for result in results[:2]:  # Show top 2 results
                file_name = Path(result['file_path']).name
                print(f"  ✓ {file_name} - Score: {result['score']:.3f}")
        else:
            print("  No results found")
        
        print("-" * 40)

def main():
    """Main function"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Advanced Chunked Medical Search')
    parser.add_argument('--interactive', action='store_true', 
                       help='Run interactive search interface')
    parser.add_argument('--demo', action='store_true',
                       help='Run demonstration of targeted searches')
    parser.add_argument('--query', type=str,
                       help='Perform a single comprehensive search')
    
    args = parser.parse_args()
    
    if args.interactive:
        interactive_search()
    elif args.demo:
        demo_targeted_searches()
    elif args.query:
        search_system = ChunkedMedicalSearch()
        search_system.initialize()
        results = search_system.comprehensive_search(args.query)
        search_system.print_search_results(results, f"Results for: {args.query}")
    else:
        print("Usage:")
        print("  python chunked_search.py --interactive    # Interactive search")
        print("  python chunked_search.py --demo          # Demo targeted searches")
        print("  python chunked_search.py --query 'text'  # Single search")

if __name__ == "__main__":
    main()
