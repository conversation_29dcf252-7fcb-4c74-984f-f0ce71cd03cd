@tailwind base;
@tailwind components;
@tailwind utilities;

/* Modal Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes fadeOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}

@keyframes modalSlideIn {
  from {
    transform: translate(-50%, -50%) scale(0.1);
    opacity: 0;
  }
  to {
    transform: translate(-50%, -50%) scale(1);
    opacity: 1;
  }
}

@keyframes modalSlideOut {
  from {
    transform: translate(-50%, -50%) scale(1);
    opacity: 1;
  }
  to {
    transform: translate(-50%, -50%) scale(0.1);
    opacity: 0;
  }
}

/* Widget morphing animation styles */
.widget-morphing {
  position: relative;
  overflow: visible !important;
}

.widget-morphing::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 0.75rem;
  backdrop-filter: blur(2px);
  z-index: 1;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.widget-morphing.animating::after {
  opacity: 1;
}

/* CSS Custom Properties for Theme Variables */
:root {
  --bg-primary: #f8f9fa;
  --bg-secondary: #ffffff;
  --bg-nav: #eef2f5;
  --text-primary: #212529;
  --text-secondary: #495057;
  --text-muted: #6c757d;
  --accent-color: #007bff;
  --accent-hover: #0056b3;
  --border-color: #dee2e6;
  --soft-border: #e9ecef;
  --user-bubble: #007bff;
  --user-text: #ffffff;
  --bot-bubble: #e9ecef;
  --bot-text: #212529;
  --header-shadow: rgba(0,0,0,0.03);
  --button-shadow: rgba(0, 0, 0, 0.05);
}

/* Dark Theme */
.dark {
  --bg-primary: #121212;
  --bg-secondary: #1e1e1e;
  --bg-nav: #242424;
  --text-primary: #e0e0e0;
  --text-secondary: #b0b0b0;
  --text-muted: #888888;
  --accent-color: #0d6efd;
  --accent-hover: #3b82f6;
  --border-color: #343a40;
  --soft-border: #2c2f33;
  --user-bubble: #0d6efd;
  --user-text: #f0f0f0;
  --bot-bubble: #343a40;
  --bot-text: #e0e0e0;
  --header-shadow: rgba(0,0,0,0.1);
  --button-shadow: rgba(255, 255, 255, 0.05);
}

/* Base styles */
body {
  margin: 0;
  font-family: 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: var(--bg-primary);
  color: var(--text-primary);
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background-color: var(--border-color);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background-color: var(--accent-color);
}

/* Loading shimmer animation - Original effect */
.loading-shimmer-text {
  font-size: 1rem; /* text-base equivalent */
  font-weight: 600;
  background-image: linear-gradient(
    -75deg,
    rgba(178, 178, 178, 0.8) 30%,
    rgba(240, 240, 240, 0.95) 50%,
    rgba(178, 178, 178, 0.8) 70%
  );
  background-size: 200% auto;
  color: transparent;
  background-clip: text;
  -webkit-background-clip: text;
  animation: textShimmer 1.8s linear infinite;
}

@keyframes textShimmer {
  to {
    background-position: -200% center;
  }
}

/* LLM processing thoughts indicator */
.llm-processing-thoughts {
  font-style: italic;
  font-size: 0.875rem; /* text-sm equivalent */
  color: #6366f1;
  margin-top: 8px;
  padding: 5px 0;
  position: relative;
  display: inline-block;
}

.dark .llm-processing-thoughts {
  color: #818cf8;
}

.llm-processing-thoughts::after {
  content: '●●●';
  display: inline-block;
  animation: thinking-pulse 1.4s infinite ease-in-out;
  margin-left: 6px;
  opacity: 0.6;
  font-size: 0.8em;
}

/* Enhanced thinking animations */
@keyframes thinking-pulse {
  0%, 100% {
    opacity: 0.3;
  }
  50% {
    opacity: 0.9;
  }
}

/* Streaming text effects */
.streaming-text {
  animation: fadeInUp 0.3s ease-out;
}

.streaming-chunk {
  animation: typewriter 0.1s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes typewriter {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Chat message animations */
.chat-message {
  opacity: 0;
  transform: translateY(15px);
  animation: fadeIn 0.4s ease-out forwards;
}

.chat-message-wrapper {
  opacity: 0;
  transform: translateY(10px);
  animation: slideInUp 0.3s ease-out forwards;
}

.chat-messages-container {
  transition: all 0.3s ease-in-out;
}

@keyframes fadeIn {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* SQL code container animations */
.sql-code-container {
  max-height: 0;
  overflow: hidden;
  opacity: 0;
  transition: max-height 0.4s ease-in-out, opacity 0.4s ease-in-out, margin-top 0.3s ease-in-out, padding 0.3s ease-in-out;
}

.sql-code-container.expanded {
  max-height: 500px;
  opacity: 1;
  margin-top: 8px;
  padding: 10px;
}

/* Chart containers */
.interactive-chart {
  border-radius: 12px;
  overflow: hidden !important;
  background-color: var(--bg-primary);
  transition: all 0.3s ease;
}

/* Enhanced dark mode chart styling */
.dark .interactive-chart {
  background-color: #1F2937;
  border-color: #374151;
}

/* Chart loading and error states */
.chart-loading {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

.dark .chart-loading {
  background: linear-gradient(90deg, #374151 25%, #4B5563 50%, #374151 75%);
  background-size: 200% 100%;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Streaming cursor effect */
.streaming-cursor {
  display: inline-block;
  width: 2px;
  height: 1em;
  background-color: #3b82f6;
  animation: blink 1s infinite;
  margin-left: 2px;
}

.dark .streaming-cursor {
  background-color: #60a5fa;
}

@keyframes blink {
  0%, 50% {
    opacity: 1;
  }
  51%, 100% {
    opacity: 0;
  }
}

/* Enhanced message transitions */
.chat-message {
  transition: all 0.3s ease;
}

.chat-message:hover {
  transform: translateY(-1px);
}

/* Smooth content updates */
.streaming-text {
  transition: all 0.2s ease;
}

/* Responsive utilities */
@media (max-width: 768px) {
  .mobile-hidden {
    display: none;
  }
}
