#!/usr/bin/env python3
"""
Simple Migration Runner
======================

This script runs the database migration without interactive prompts.
Use this for automated deployments or when you're certain you want to proceed.
"""

import sys
import os

# Add the current directory to path so we can import the migrator
sys.path.append(os.path.dirname(__file__))

from database_purge_and_migrate import DatabaseMigrator

def main():
    """Run migration without prompts"""
    print("🚀 Running database migration...")
    
    migrator = DatabaseMigrator()
    success = migrator.run_migration()
    
    if success:
        print("✅ Migration completed successfully!")
        return True
    else:
        print("❌ Migration failed!")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
