#!/usr/bin/env python3
"""
Test Migration and Sync Integration
===================================

This script tests that the migration and sync scripts work together properly.
It verifies role compatibility and data consistency.
"""

import sys
import os
import psycopg2

# Add backend directory to path to import config
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'backend'))

try:
    from config import Config
except ImportError:
    print("Error: Could not import config.py. Make sure you're running this from the project root.")
    sys.exit(1)

def test_role_compatibility():
    """Test that migration and sync scripts create compatible roles"""
    config = Config()
    
    try:
        # Connect to app database
        db_config = config.app_database_config
        connection = psycopg2.connect(
            host=db_config['host'],
            database=db_config['dbname'],
            user=db_config['user'],
            password=db_config['password'],
            port=db_config['port']
        )
        
        cursor = connection.cursor()
        
        print("🧪 Testing Migration and Sync Compatibility")
        print("=" * 50)
        
        # Check if required roles exist
        required_roles = ['admin', 'head_of_department', 'medical_staff']
        
        cursor.execute("SELECT name, description FROM role ORDER BY name")
        existing_roles = cursor.fetchall()
        
        print(f"📋 Existing roles: {len(existing_roles)}")
        existing_role_names = set()
        for name, desc in existing_roles:
            print(f"   • {name}: {desc}")
            existing_role_names.add(name)
        
        print("\n" + "=" * 50)
        
        # Check role compatibility
        missing_roles = set(required_roles) - existing_role_names
        if missing_roles:
            print(f"❌ Missing required roles: {missing_roles}")
            print("   Run the migration script first!")
            return False
        else:
            print("✅ All required roles present")
        
        # Check if medical_staff role exists (not 'doctor')
        if 'medical_staff' in existing_role_names:
            print("✅ Correct role name 'medical_staff' found (not 'doctor')")
        else:
            print("❌ Role 'medical_staff' not found - sync script may use wrong role name")
            return False
        
        # Check topics
        cursor.execute("SELECT COUNT(*) FROM topic")
        topic_count = cursor.fetchone()[0]
        print(f"🏷️  Medical topics: {topic_count}")
        
        if topic_count >= 10:
            print("✅ Sufficient medical topics created")
        else:
            print("⚠️  Few topics created - may need to run migration script")
        
        # Check departments (should be empty after migration, populated after sync)
        cursor.execute("SELECT COUNT(*) FROM department")
        dept_count = cursor.fetchone()[0]
        print(f"🏢 Departments: {dept_count}")
        
        if dept_count == 0:
            print("✅ No departments (expected after migration, before sync)")
        else:
            print(f"✅ {dept_count} departments (expected after sync)")
        
        # Check users
        cursor.execute("SELECT COUNT(*) FROM \"user\"")
        user_count = cursor.fetchone()[0]
        print(f"👤 Users: {user_count}")
        
        print("\n" + "=" * 50)
        print("🎯 Compatibility Test Results:")
        
        if missing_roles:
            print("❌ FAILED: Missing required roles")
            print("   Solution: Run database_purge_and_migrate.py first")
        elif 'medical_staff' not in existing_role_names:
            print("❌ FAILED: Wrong role names")
            print("   Solution: Update sync script to use 'medical_staff' role")
        else:
            print("✅ PASSED: Migration and sync scripts are compatible")
            print("\n📝 Recommended workflow:")
            print("   1. Run: python scripts/database_purge_and_migrate.py")
            print("   2. Run: python scripts/sync_hospital_data.py")
            print("   3. Test: python scripts/verify_migration.py")
        
        cursor.close()
        connection.close()
        
        return len(missing_roles) == 0 and 'medical_staff' in existing_role_names
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

def main():
    """Main test function"""
    print("🔍 Testing migration and sync script compatibility...")
    
    success = test_role_compatibility()
    
    if success:
        print("\n✅ All tests passed!")
        return True
    else:
        print("\n❌ Tests failed!")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
