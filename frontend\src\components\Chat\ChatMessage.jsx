import { useState } from 'react';
import DataTable from './DataTable';
import Chart from './Chart';

const ChatMessage = ({ message }) => {
  const [showSqlDetails, setShowSqlDetails] = useState(false);

  const isUser = message.type === 'user';

  const formatTimestamp = (timestamp) => {
    return new Date(timestamp).toLocaleTimeString();
  };

  const toggleSqlDetails = () => {
    setShowSqlDetails(!showSqlDetails);
  };

  // Hide the message if isHidden is true
  if (message.isHidden) {
    return null;
  }

  return (
    <div className={`chat-message mb-4 animate-fade-in ${isUser ? 'ml-auto' : 'mr-auto'} max-w-3xl`}>
      <div className={`
        p-3 rounded-2xl shadow-sm
        ${isUser
          ? 'bg-blue-600 text-white rounded-br-md'
          : 'bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 border border-gray-200 dark:border-gray-700 rounded-bl-md'
        }
      `}>
        {/* Message Header - Hide if hideAssistantMessage is true */}
        {!message.hideAssistantMessage && (
          <div className="flex items-center mb-3">
            <strong className="text-base font-semibold">
              {isUser ? 'You: ' : 'Assistant: '}
            </strong>
            <span className="text-sm opacity-70 ml-2">
              {formatTimestamp(message.timestamp)}
            </span>
          </div>
        )}

        {/* Loading State - Show "Assistant is thinking..." when no content yet */}
        {message.isLoading && (
          <div className="flex items-center">
            <span className="loading-shimmer-text">
              {message.executionStatus || message.summaryLoading || message.chartLoading || 'Assistant is thinking...'}
            </span>
          </div>
        )}

        {/* Thinking State - Show "Processing thoughts..." when AI is actively thinking */}
        {message.isThinking && !message.isLoading && (
          <div className="llm-processing-thoughts">
            Processing thoughts
          </div>
        )}

        {/* Regular Message Content */}
        {!message.isLoading && !message.isThinking && (
          <>
            {/* Main Content - Hide if hideAssistantMessage is true */}
            {!message.hideAssistantMessage && (
              <div className={`text-base streaming-text ${message.isError ? 'text-red-600 dark:text-red-400' : ''}`}>
                {message.content}
                {message.isStreaming && (
                  <span className="inline-block w-2 h-4 bg-blue-500 dark:bg-blue-400 ml-1 animate-pulse"></span>
                )}
              </div>
            )}

            {/* Grouped Intermediate SQL Processing */}
            {message.isIntermediateProcessing && (
              <div className="mt-4 space-y-4">
                {/* Notification */}
                {message.intermediateNotification && (
                  <div className="p-3 bg-amber-50 dark:bg-amber-900 border border-amber-200 dark:border-amber-700 rounded-lg">
                    <div className="flex items-start">
                      <span className="text-lg text-amber-600 dark:text-amber-400 mr-3 mt-0.5">💡</span>
                      <div>
                        <strong className="text-base font-semibold text-amber-800 dark:text-amber-200 block mb-1">
                          Smart Query Processing
                        </strong>
                        <p className="text-sm text-amber-700 dark:text-amber-300">
                          {message.intermediateNotification}
                        </p>
                      </div>
                    </div>
                  </div>
                )}

                {/* Multiple Intermediate SQL Queries */}
                {message.intermediateQueries && message.intermediateQueries.length > 0 && (
                  <div className="space-y-4">
                    {message.intermediateQueries.map((query, index) => {
                      if (!query) return null;

                      const result = message.intermediateResults && message.intermediateResults[index];
                      const iterationNumber = index + 1;

                      return (
                        <div key={index} className="border border-gray-200 dark:border-gray-600 rounded-lg overflow-hidden">
                          {/* Intermediate SQL Query */}
                          <div>
                            <div className="mb-2 p-3 bg-blue-50 dark:bg-blue-900 border-b border-blue-200 dark:border-blue-700">
                              <div className="flex items-center">
                                <span className="text-base text-blue-600 dark:text-blue-400 mr-2">🔍</span>
                                <strong className="text-base font-semibold text-blue-800 dark:text-blue-200">
                                  Intermediate Query #{iterationNumber}
                                </strong>
                              </div>
                              {query.message && (
                                <p className="text-sm text-blue-600 dark:text-blue-400 mt-1">{query.message}</p>
                              )}
                            </div>
                            <pre className="bg-blue-50 dark:bg-blue-900 p-3 text-base overflow-x-auto">
                              <code className="text-base text-blue-600 dark:text-blue-400">
                                {query.sql}
                              </code>
                            </pre>
                          </div>

                          {/* Intermediate Results */}
                          {result && result.results && (
                            <div>
                              <div className="p-3 bg-green-50 dark:bg-green-900 border-t border-green-200 dark:border-green-700">
                                <div className="flex items-center">
                                  <span className="text-base text-green-600 dark:text-green-400 mr-2">📊</span>
                                  <strong className="text-base font-semibold text-green-800 dark:text-green-200">
                                    Results #{iterationNumber} ({result.totalRows} rows found)
                                  </strong>
                                </div>
                                {result.message && (
                                  <p className="text-sm text-green-600 dark:text-green-400 mt-1">{result.message}</p>
                                )}
                              </div>
                              {result.results.length > 0 && (
                                <div className="bg-white dark:bg-gray-800 overflow-hidden">
                                  <table className="w-full text-sm">
                                    <thead className="bg-green-100 dark:bg-green-800">
                                      <tr>
                                        {Object.keys(result.results[0]).map((key) => (
                                <th key={key} className="px-3 py-2 text-left text-xs font-semibold text-green-800 dark:text-green-200 uppercase tracking-wider">
                                  {key}
                                </th>
                              ))}
                            </tr>
                          </thead>
                          <tbody className="divide-y divide-green-200 dark:divide-green-700">
                            {result.results.slice(0, 5).map((row, rowIndex) => (
                              <tr key={rowIndex} className="hover:bg-green-50 dark:hover:bg-green-900">
                                {Object.values(row).map((value, cellIndex) => (
                                  <td key={cellIndex} className="px-3 py-2 text-green-900 dark:text-green-100">
                                    {value !== null && value !== undefined ? String(value) : '—'}
                                  </td>
                                ))}
                              </tr>
                            ))}
                          </tbody>
                        </table>
                        {result.totalRows > 5 && (
                          <div className="px-3 py-2 bg-green-50 dark:bg-green-800 text-xs text-green-600 dark:text-green-300 text-center">
                            Showing first 5 of {result.totalRows} rows
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                          )}
                        </div>
                      );
                    })}
                  </div>
                )}

                {/* Final Transition */}
                {message.finalTransitionMessage && (
                  <div className="p-3 bg-purple-50 dark:bg-purple-900 border border-purple-200 dark:border-purple-700 rounded-lg">
                    <div className="flex items-start">
                      <span className="text-lg text-purple-600 dark:text-purple-400 mr-3 mt-0.5">🎯</span>
                      <div>
                        <strong className="text-base font-semibold text-purple-800 dark:text-purple-200 block mb-1">
                          Final Query Generation
                        </strong>
                        <p className="text-sm text-purple-700 dark:text-purple-300">
                          {message.finalTransitionMessage}
                        </p>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            )}

            {/* Individual Intermediate Step */}
            {message.isIntermediateStep && (
              <div className="border border-gray-200 dark:border-gray-600 rounded-lg overflow-hidden">
                {/* Intermediate SQL Query */}
                <div>
                  <div className="p-3 bg-blue-50 dark:bg-blue-900 border-b border-blue-200 dark:border-blue-700">
                    <div className="flex items-center">
                      <span className="text-base text-blue-600 dark:text-blue-400 mr-2">🔍</span>
                      <strong className="text-base font-semibold text-blue-800 dark:text-blue-200">
                        Intermediate Query #{message.iteration}
                      </strong>
                    </div>
                    {message.intermediateSqlMessage && (
                      <p className="text-sm text-blue-600 dark:text-blue-400 mt-1">{message.intermediateSqlMessage}</p>
                    )}
                  </div>
                  {message.intermediateSql && (
                    <pre className="bg-blue-50 dark:bg-blue-900 p-3 text-base overflow-x-auto">
                      <code className="text-base text-blue-600 dark:text-blue-400">
                        {message.intermediateSql}
                      </code>
                    </pre>
                  )}
                </div>

                {/* Intermediate Results */}
                {message.hasResults && message.intermediateResults && (
                  <div>
                    <div className="p-3 bg-green-50 dark:bg-green-900 border-t border-green-200 dark:border-green-700">
                      <div className="flex items-center">
                        <span className="text-base text-green-600 dark:text-green-400 mr-2">📊</span>
                        <strong className="text-base font-semibold text-green-800 dark:text-green-200">
                          Results #{message.iteration} ({message.totalRows} rows found)
                        </strong>
                      </div>
                      {message.intermediateResultsMessage && (
                        <p className="text-sm text-green-600 dark:text-green-400 mt-1">{message.intermediateResultsMessage}</p>
                      )}
                    </div>
                    {message.intermediateResults.length > 0 && (
                      <div className="bg-white dark:bg-gray-800 overflow-hidden">
                        <table className="w-full text-sm">
                          <thead className="bg-green-100 dark:bg-green-800">
                            <tr>
                              {Object.keys(message.intermediateResults[0]).map((key) => (
                                <th key={key} className="px-3 py-2 text-left text-xs font-semibold text-green-800 dark:text-green-200 uppercase tracking-wider">
                                  {key}
                                </th>
                              ))}
                            </tr>
                          </thead>
                          <tbody className="divide-y divide-green-200 dark:divide-green-700">
                            {message.intermediateResults.slice(0, 5).map((row, rowIndex) => (
                              <tr key={rowIndex} className="hover:bg-green-50 dark:hover:bg-green-900">
                                {Object.values(row).map((value, cellIndex) => (
                                  <td key={cellIndex} className="px-3 py-2 text-green-900 dark:text-green-100">
                                    {value !== null && value !== undefined ? String(value) : '—'}
                                  </td>
                                ))}
                              </tr>
                            ))}
                          </tbody>
                        </table>
                        {message.totalRows > 5 && (
                          <div className="px-3 py-2 bg-green-50 dark:bg-green-800 text-xs text-green-600 dark:text-green-300 text-center">
                            Showing first 5 of {message.totalRows} rows
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                )}
              </div>
            )}

            {/* Thinking/Processing Message */}
            {message.isThinking && (
              <div className="p-3 bg-purple-50 dark:bg-purple-900 border border-purple-200 dark:border-purple-700 rounded-lg">
                <div className="flex items-start">
                  <span className="text-lg text-purple-600 dark:text-purple-400 mr-3 mt-0.5">🤔</span>
                  <div>
                    <strong className="text-base font-semibold text-purple-800 dark:text-purple-200 block mb-1">
                      Assistant is thinking...
                    </strong>
                    <p className="text-sm text-purple-700 dark:text-purple-300">
                      {message.thinkingMessage || 'Processing thoughts and generating next step...'}
                    </p>
                  </div>
                </div>
              </div>
            )}

            {/* SQL Query Display */}
            {message.showSql && message.sqlQuery && !message.isIntermediate && (
              <div>
                <div className="mb-3 flex items-center justify-between">
                  <div>
                    <strong className="text-base font-semibold">Assistant:</strong>
                    <span className="ml-2 text-sm text-gray-500 dark:text-gray-400">
                      {message.sqlTimestamp || new Date().toLocaleTimeString()}
                    </span>
                  </div>
                </div>

                {/* Intermediate SQL Info */}
                {message.intermediateInfo && (
                  <div className="mb-3 p-3 bg-green-50 dark:bg-green-900 border border-green-200 dark:border-green-700 rounded-md">
                    <div className="flex items-center mb-2">
                      <span className="text-base text-green-600 dark:text-green-400 mr-2">✅</span>
                      <strong className="text-base font-semibold text-green-800 dark:text-green-200">Smart Query Generation</strong>
                    </div>
                    <p className="text-sm text-green-700 dark:text-green-300">
                      {Array.isArray(message.intermediateInfo.resultsCount)
                        ? `Used ${message.intermediateInfo.resultsCount.length} intermediate exploration steps to discover data options and generated this optimized query.`
                        : `Used intermediate exploration to discover ${message.intermediateInfo.resultsCount} data options and generated this optimized query.`
                      }
                    </p>
                  </div>
                )}

                <div className="mb-3">
                  <strong className="text-base font-semibold">
                    {message.intermediateInfo ? 'Final SQL Query:' : 'Extracted SQL:'}
                  </strong>
                </div>
                <pre className="bg-gray-100 dark:bg-gray-900 p-3 rounded-md text-base overflow-x-auto border">
                  <code className="text-base text-blue-600 dark:text-blue-400">
                    {message.sqlQuery}
                  </code>
                </pre>

                {/* Toggle button for full LLM output */}
                {message.content && (
                  <button
                    onClick={toggleSqlDetails}
                    className="mt-2 px-3 py-1 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded transition-colors"
                  >
                    {showSqlDetails ? 'Hide Full LLM Output' : 'Show Full LLM Output'}
                  </button>
                )}

                {/* Collapsible LLM Output */}
                {showSqlDetails && (
                  <div className="mt-3 p-3 bg-gray-50 dark:bg-gray-800 rounded-md border">
                    <pre className="text-sm whitespace-pre-wrap text-gray-700 dark:text-gray-300">
                      {message.content}
                    </pre>
                  </div>
                )}
              </div>
            )}

            {/* Corrected SQL Display */}
            {message.correctedSqlInfo && (
              <div className="mt-4">
                <div className="mb-3 p-3 bg-yellow-50 dark:bg-yellow-900 border border-yellow-200 dark:border-yellow-700 rounded-md">
                  <div className="flex items-center mb-2">
                    <span className="text-base text-yellow-600 dark:text-yellow-400 mr-2">⚠️</span>
                    <strong className="text-base font-semibold text-yellow-800 dark:text-yellow-200">
                      {message.correctedSqlInfo.bothFailed ? 'SQL Execution Failed - Both Attempts' : 'SQL Auto-Correction Applied'}
                    </strong>
                  </div>
                  <p className="text-sm text-yellow-700 dark:text-yellow-300">
                    {message.correctedSqlInfo.bothFailed
                      ? 'The initial SQL failed and the automatic correction attempt also failed.'
                      : 'The initial SQL failed but was automatically corrected and executed successfully.'
                    }
                  </p>
                </div>

                <div className="mb-2">
                  <strong className="text-base font-semibold text-red-600 dark:text-red-400">Initial SQL (Failed):</strong>
                </div>
                <pre className="bg-red-50 dark:bg-red-900 p-3 rounded-md text-base overflow-x-auto border border-red-200 dark:border-red-700">
                  <code className="text-base text-red-600 dark:text-red-400">
                    {message.correctedSqlInfo.originalSql}
                  </code>
                </pre>

                <div className="mb-2 mt-4">
                  <strong className={`text-base font-semibold ${message.correctedSqlInfo.bothFailed
                    ? "text-red-600 dark:text-red-400"
                    : "text-green-600 dark:text-green-400"}`}>
                    Corrected SQL {message.correctedSqlInfo.bothFailed ? '(Also Failed)' : '(Executed Successfully)'}:
                  </strong>
                </div>
                <pre className={`p-3 rounded-md text-base overflow-x-auto border ${
                  message.correctedSqlInfo.bothFailed
                    ? 'bg-red-50 dark:bg-red-900 border-red-200 dark:border-red-700'
                    : 'bg-green-50 dark:bg-green-900 border-green-200 dark:border-green-700'
                }`}>
                  <code className={`text-base ${message.correctedSqlInfo.bothFailed
                    ? "text-red-600 dark:text-red-400"
                    : "text-green-600 dark:text-green-400"}`}>
                    {message.correctedSqlInfo.correctedSql}
                  </code>
                </pre>
              </div>
            )}

            {/* Failed SQL Display */}
            {message.failedSql && !message.correctedSqlInfo && (
              <div className="mt-4">
                <div className="mb-3 p-3 bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-700 rounded-md">
                  <div className="flex items-center mb-2">
                    <span className="text-base text-red-600 dark:text-red-400 mr-2">❌</span>
                    <strong className="text-base font-semibold text-red-800 dark:text-red-200">SQL Execution Failed</strong>
                  </div>
                  <p className="text-sm text-red-700 dark:text-red-300">
                    The generated SQL query could not be executed. Please try rephrasing your question or check if the requested data exists.
                  </p>
                </div>

                <div className="mb-2">
                  <strong className="text-base font-semibold text-red-600 dark:text-red-400">Failed SQL Query:</strong>
                </div>
                <pre className="bg-red-50 dark:bg-red-900 p-3 rounded-md text-base overflow-x-auto border border-red-200 dark:border-red-700">
                  <code className="text-base text-red-600 dark:text-red-400">
                    {message.failedSql}
                  </code>
                </pre>
              </div>
            )}

            {/* Data Table */}
            {message.tableData && (
              <div className="mt-4">
                <DataTable data={message.tableData} />
              </div>
            )}

            {/* Chart */}
            {message.chartData && (
              <div className="mt-4">
                <Chart data={message.chartData} />
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
};

export default ChatMessage;
