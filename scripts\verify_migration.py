#!/usr/bin/env python3
"""
Migration Verification Script
============================

This script verifies that the migration completed successfully and shows
the hospital-specific data that was created.
"""

import sys
import os
import psycopg2

# Add backend directory to path to import config
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'backend'))

try:
    from config import Config
except ImportError:
    print("Error: Could not import config.py. Make sure you're running this from the project root.")
    sys.exit(1)

def verify_migration():
    """Verify migration and show created data"""
    config = Config()
    
    try:
        # Connect to database
        db_config = config.app_database_config
        connection = psycopg2.connect(
            host=db_config['host'],
            database=db_config['dbname'],
            user=db_config['user'],
            password=db_config['password'],
            port=db_config['port']
        )
        
        cursor = connection.cursor()
        
        print("🏥 Hospital Database Migration Verification")
        print("=" * 50)
        
        # Check tables
        cursor.execute("""
            SELECT table_name FROM information_schema.tables 
            WHERE table_schema = 'public' 
            ORDER BY table_name
        """)
        tables = [row[0] for row in cursor.fetchall()]
        print(f"📊 Tables created: {len(tables)}")
        for table in tables:
            print(f"   • {table}")
        
        print("\n" + "=" * 50)
        
        # Check roles
        cursor.execute("SELECT name, description FROM role ORDER BY name")
        roles = cursor.fetchall()
        print(f"👥 Hospital Roles: {len(roles)}")
        for name, desc in roles:
            print(f"   • {name}: {desc}")
        
        print("\n" + "=" * 50)
        
        # Check topics
        cursor.execute("SELECT name, description FROM topic ORDER BY name")
        topics = cursor.fetchall()
        print(f"🏷️  Medical Topics: {len(topics)}")
        for name, desc in topics:
            print(f"   • {name}: {desc}")
        
        print("\n" + "=" * 50)
        
        # Check departments (should be empty initially)
        cursor.execute("SELECT COUNT(*) FROM department")
        dept_count = cursor.fetchone()[0]
        print(f"🏢 Departments: {dept_count} (will be synced from hospital DB)")
        
        # Check users (should be empty initially)
        cursor.execute("SELECT COUNT(*) FROM \"user\"")
        user_count = cursor.fetchone()[0]
        print(f"👤 Users: {user_count} (will be created via sync script)")
        
        print("\n✅ Migration verification completed!")
        print("📝 Next steps:")
        print("   1. Run sync_hospital_data.py to populate departments and users")
        print("   2. Test the chatbot with medical data queries")
        print("   3. Verify analytics dashboard functionality")
        
        cursor.close()
        connection.close()
        
        return True
        
    except Exception as e:
        print(f"❌ Verification failed: {e}")
        return False

if __name__ == "__main__":
    success = verify_migration()
    sys.exit(0 if success else 1)
