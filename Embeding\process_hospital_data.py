"""
Hospital Medical Records Processing Script

This script processes the medical records dataset for the hospital chatbot,
focusing on improving embedding quality by removing redundant information
and extracting meaningful clinical content.
"""

import os
import sys
import json
import logging
from pathlib import Path
from typing import List, Dict, Any
from tqdm import tqdm

# Add the current directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from medical_record_embedder import MedicalRecordEmbedder, MedicalRecord
from config import *

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('medical_processing.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class HospitalDataProcessor:
    """
    Specialized processor for hospital medical records
    """
    
    def __init__(self):
        self.embedder = MedicalRecordEmbedder(model_name=EMBEDDING_MODEL)
        self.stats = {
            'total_files': 0,
            'successfully_parsed': 0,
            'duplicates_removed': 0,
            'embeddings_created': 0,
            'specialties_distribution': {}
        }
        
    def analyze_dataset(self, dataset_path: str) -> Dict[str, Any]:
        """
        Analyze the dataset to understand its structure and content
        
        Args:
            dataset_path: Path to the dataset directory
            
        Returns:
            Analysis report
        """
        dataset_path = Path(dataset_path)
        md_files = list(dataset_path.glob("*.md"))
        
        analysis = {
            'total_files': len(md_files),
            'file_categories': {
                'data_': 0,
                'document_': 0,
                'Medical_Report_': 0,
                'Sample_medical_report_': 0,
                'sample': 0
            },
            'content_analysis': {
                'avg_length': 0,
                'common_diagnoses': {},
                'common_medications': {},
                'departments': {}
            }
        }
        
        total_length = 0
        all_diagnoses = []
        all_medications = []
        all_departments = []
        
        for file_path in tqdm(md_files, desc="Analyzing files"):
            filename = file_path.name
            
            # Categorize files
            if filename.startswith('data_'):
                analysis['file_categories']['data_'] += 1
            elif filename.startswith('document_'):
                analysis['file_categories']['document_'] += 1
            elif filename.startswith('Medical_Report_'):
                analysis['file_categories']['Medical_Report_'] += 1
            elif filename.startswith('Sample medical report'):
                analysis['file_categories']['Sample_medical_report_'] += 1
            elif filename.startswith('sample'):
                analysis['file_categories']['sample'] += 1
            
            # Analyze content
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    total_length += len(content)
                    
                    # Extract information for analysis
                    record = self.embedder.parse_medical_record(str(file_path))
                    if record:
                        if record.primary_diagnosis:
                            all_diagnoses.append(record.primary_diagnosis)
                        all_medications.extend(record.medications)
                        all_departments.append(record.department)
                        
            except Exception as e:
                logger.warning(f"Error analyzing {file_path}: {e}")
        
        # Calculate statistics
        analysis['content_analysis']['avg_length'] = total_length / len(md_files) if md_files else 0
        
        # Count common elements
        from collections import Counter
        diagnosis_counts = Counter(all_diagnoses)
        medication_counts = Counter(all_medications)
        department_counts = Counter(all_departments)
        
        analysis['content_analysis']['common_diagnoses'] = dict(diagnosis_counts.most_common(10))
        analysis['content_analysis']['common_medications'] = dict(medication_counts.most_common(10))
        analysis['content_analysis']['departments'] = dict(department_counts.most_common(10))
        
        return analysis
    
    def process_and_embed(self, dataset_path: str, output_path: str = None) -> bool:
        """
        Process the dataset and create embeddings
        
        Args:
            dataset_path: Path to the dataset
            output_path: Optional path to save processed data
            
        Returns:
            True if successful, False otherwise
        """
        try:
            logger.info("Starting dataset processing...")
            
            # Step 1: Analyze dataset
            logger.info("Analyzing dataset structure...")
            analysis = self.analyze_dataset(dataset_path)
            self.stats['total_files'] = analysis['total_files']
            
            logger.info(f"Dataset analysis complete:")
            logger.info(f"- Total files: {analysis['total_files']}")
            logger.info(f"- File categories: {analysis['file_categories']}")
            logger.info(f"- Average file length: {analysis['content_analysis']['avg_length']:.0f} chars")
            
            # Step 2: Process records
            logger.info("Processing medical records...")
            records = self.embedder.process_dataset(dataset_path)
            self.stats['successfully_parsed'] = len(records)
            
            # Step 3: Calculate specialty distribution
            specialty_counts = {}
            for record in records:
                specialty = record.record_type
                specialty_counts[specialty] = specialty_counts.get(specialty, 0) + 1
            self.stats['specialties_distribution'] = specialty_counts
            
            logger.info(f"Record processing complete:")
            logger.info(f"- Successfully parsed: {len(records)} records")
            logger.info(f"- Specialty distribution: {specialty_counts}")
            
            # Step 4: Save processed data if output path specified
            if output_path:
                self.save_processed_data(records, output_path, analysis)
            
            # Step 5: Create embeddings
            logger.info("Creating embeddings...")
            embeddings = self.embedder.create_embeddings(records)
            self.stats['embeddings_created'] = len(embeddings)
            
            # Step 6: Setup Qdrant and store embeddings
            logger.info("Setting up Qdrant database...")
            self.embedder.setup_qdrant(host=QDRANT_HOST, port=QDRANT_PORT)
            
            logger.info("Storing embeddings in Qdrant...")
            self.embedder.store_in_qdrant(embeddings)
            
            logger.info("Processing complete!")
            self.print_final_stats()
            
            return True
            
        except Exception as e:
            logger.error(f"Error during processing: {e}")
            return False
    
    def save_processed_data(self, records: List[MedicalRecord], output_path: str, analysis: Dict):
        """
        Save processed data to JSON files
        
        Args:
            records: List of processed medical records
            output_path: Output directory path
            analysis: Dataset analysis results
        """
        output_path = Path(output_path)
        output_path.mkdir(exist_ok=True)
        
        # Save records
        records_data = []
        for record in records:
            records_data.append({
                'patient_id': record.patient_id,
                'record_type': record.record_type,
                'primary_diagnosis': record.primary_diagnosis,
                'medications': record.medications,
                'specialized_content': record.specialized_content,
                'file_path': record.file_path
            })
        
        with open(output_path / 'processed_records.json', 'w', encoding='utf-8') as f:
            json.dump(records_data, f, ensure_ascii=False, indent=2)
        
        # Save analysis
        with open(output_path / 'dataset_analysis.json', 'w', encoding='utf-8') as f:
            json.dump(analysis, f, ensure_ascii=False, indent=2)
        
        logger.info(f"Processed data saved to {output_path}")
    
    def print_final_stats(self):
        """Print final processing statistics"""
        logger.info("=== FINAL PROCESSING STATISTICS ===")
        logger.info(f"Total files processed: {self.stats['total_files']}")
        logger.info(f"Successfully parsed: {self.stats['successfully_parsed']}")
        logger.info(f"Embeddings created: {self.stats['embeddings_created']}")
        logger.info(f"Specialty distribution:")
        for specialty, count in self.stats['specialties_distribution'].items():
            logger.info(f"  - {specialty}: {count} records")
    
    def test_query_system(self, test_queries: List[str]):
        """
        Test the query system with sample queries
        
        Args:
            test_queries: List of test queries
        """
        logger.info("=== TESTING QUERY SYSTEM ===")
        
        for query in test_queries:
            logger.info(f"\nQuery: {query}")
            try:
                results = self.embedder.query_similar_records(query, limit=3)
                logger.info("Results:")
                for i, result in enumerate(results, 1):
                    logger.info(f"  {i}. Score: {result['score']:.3f} - {result['summary']}")
                    logger.info(f"     File: {result['record']['file_path']}")
            except Exception as e:
                logger.error(f"Error querying: {e}")

def main():
    """Main function"""
    processor = HospitalDataProcessor()
    
    # Test queries for the hospital dataset
    test_queries = [
        "patient with chest pain and heart problems",
        "intoxication medicamenteuse suicide",
        "douleur abdominale appendicite",
        "diabete glycemie insuline",
        "AVC neurologique deficit",
        "patient adolescent depression"
    ]
    
    # Process the dataset
    success = processor.process_and_embed(
        dataset_path=DATASET_PATH,
        output_path=PROCESSED_DATA_PATH
    )
    
    if success:
        logger.info("Dataset processing completed successfully!")
        
        # Test the query system
        processor.test_query_system(test_queries)
    else:
        logger.error("Dataset processing failed!")
        sys.exit(1)

if __name__ == "__main__":
    main()
