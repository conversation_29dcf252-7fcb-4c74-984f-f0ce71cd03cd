#!/usr/bin/env python3
"""
Cleanup Duplicate Roles Script
==============================

This script removes duplicate roles that were created by both migration and sync scripts.
"""

import sys
import os
import psycopg2

# Add backend directory to path to import config
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'backend'))

try:
    from config import Config
except ImportError:
    print("Error: Could not import config.py. Make sure you're running this from the project root.")
    sys.exit(1)

def cleanup_duplicates():
    """Remove duplicate roles keeping only the first occurrence of each"""
    config = Config()
    
    try:
        # Connect to app database
        db_config = config.app_database_config
        connection = psycopg2.connect(
            host=db_config['host'],
            database=db_config['dbname'],
            user=db_config['user'],
            password=db_config['password'],
            port=db_config['port']
        )
        
        cursor = connection.cursor()
        
        print("🧹 Cleaning up duplicate roles...")
        
        # Find and remove duplicate roles, keeping only the first occurrence
        cleanup_sql = """
        DELETE FROM role 
        WHERE id NOT IN (
            SELECT MIN(id) 
            FROM role 
            GROUP BY name
        );
        """
        
        cursor.execute(cleanup_sql)
        deleted_count = cursor.rowcount
        connection.commit()
        
        print(f"✅ Removed {deleted_count} duplicate roles")
        
        # Verify cleanup
        cursor.execute("SELECT id, name, description FROM role ORDER BY id")
        roles = cursor.fetchall()
        
        print(f"\n📋 Remaining roles ({len(roles)}):")
        for role_id, name, description in roles:
            print(f"   • {name}: {description}")
        
        cursor.close()
        connection.close()
        
        return True
        
    except Exception as e:
        print(f"❌ Cleanup failed: {e}")
        return False

def main():
    """Main cleanup function"""
    print("🧹 Starting duplicate role cleanup...")
    
    success = cleanup_duplicates()
    
    if success:
        print("\n✅ Cleanup completed successfully!")
        return True
    else:
        print("\n❌ Cleanup failed!")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
