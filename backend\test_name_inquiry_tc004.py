#!/usr/bin/env python3
"""
Test script to verify TC004 - Name inquiry functionality fix.
Tests that the chatbot responds correctly to "What's your name?" questions.
"""

import asyncio
import json
import sys
import os

# Add the backend directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from config import Config
from app.core import setup_vanna
from app.utils import MemoryCache

async def test_name_inquiry():
    """Test the name inquiry functionality for TC004"""
    print("🧪 Testing TC004 - Name inquiry functionality...")
    
    # Setup Vanna
    print("📝 Setting up Vanna...")
    vn = setup_vanna()
    
    if not vn:
        print("❌ Failed to setup Vanna")
        return False
    
    # Test cases for name inquiry
    test_cases = [
        "What's your name?",
        "What is your name?",
        "Who are you?",
        "Tell me your name",
        "What should I call you?"
    ]
    
    expected_response = "I'm your hospital assistant. How can I help?"
    
    all_passed = True
    
    for i, test_question in enumerate(test_cases, 1):
        print(f"\n🔍 Test Case {i}: '{test_question}'")
        
        try:
            # Generate response
            print("🤖 Generating response...")
            response_stream = vn.generate_interactive_response(
                question=test_question,
                current_id=f"test_name_inquiry_{i:03d}",
                allow_llm_to_see_data=True
            )
            
            # Collect the response
            response_chunks = []
            for chunk in response_stream:
                if chunk.startswith("data: "):
                    try:
                        data = json.loads(chunk[6:])  # Remove "data: " prefix
                        if data.get('type') == 'chunk':
                            response_chunks.append(data.get('content', ''))
                        elif data.get('type') == 'chat_result':
                            response_chunks.append(data.get('message', ''))
                            break
                    except json.JSONDecodeError:
                        continue
            
            final_response = ''.join(response_chunks).strip()
            
            print(f"🤖 Response: '{final_response}'")
            
            # Check if response matches expected
            response_lower = final_response.lower()
            expected_lower = expected_response.lower()
            
            # Check for exact match or close match
            is_correct = (
                expected_lower in response_lower or
                "hospital assistant" in response_lower or
                ("i'm" in response_lower and "assistant" in response_lower)
            )
            
            # Check for unwanted responses
            unwanted_keywords = ['qwen', 'alibaba', 'cloud', 'large language model']
            has_unwanted = any(keyword in response_lower for keyword in unwanted_keywords)
            
            if is_correct and not has_unwanted:
                print(f"✅ Test Case {i}: PASSED")
            else:
                print(f"❌ Test Case {i}: FAILED")
                if not is_correct:
                    print(f"   - Expected response containing: '{expected_response}'")
                    print(f"   - Got: '{final_response}'")
                if has_unwanted:
                    print(f"   - Contains unwanted keywords: {[kw for kw in unwanted_keywords if kw in response_lower]}")
                all_passed = False
                
        except Exception as e:
            print(f"❌ Test Case {i}: ERROR - {str(e)}")
            import traceback
            traceback.print_exc()
            all_passed = False
    
    return all_passed

async def test_specific_tc004():
    """Test the exact TC004 scenario"""
    print("\n" + "="*60)
    print("🎯 Testing EXACT TC004 scenario")
    print("="*60)
    
    # Setup Vanna
    vn = setup_vanna()
    if not vn:
        print("❌ Failed to setup Vanna")
        return False
    
    test_question = "What's your name?"
    expected_response = "I'm your hospital assistant. How can I help?"
    
    print(f"❓ Question: '{test_question}'")
    print(f"✅ Expected: '{expected_response}'")
    
    try:
        # Generate response
        response_stream = vn.generate_interactive_response(
            question=test_question,
            current_id="tc004_exact_test",
            allow_llm_to_see_data=True
        )
        
        # Collect the response
        response_chunks = []
        for chunk in response_stream:
            if chunk.startswith("data: "):
                try:
                    data = json.loads(chunk[6:])
                    if data.get('type') == 'chunk':
                        response_chunks.append(data.get('content', ''))
                    elif data.get('type') == 'chat_result':
                        response_chunks.append(data.get('message', ''))
                        break
                except json.JSONDecodeError:
                    continue
        
        final_response = ''.join(response_chunks).strip()
        print(f"🤖 Actual: '{final_response}'")
        
        # Check result
        response_lower = final_response.lower()
        expected_lower = expected_response.lower()
        
        # More flexible matching
        is_correct = (
            "hospital assistant" in response_lower and
            ("i'm" in response_lower or "i am" in response_lower) and
            "help" in response_lower
        )
        
        # Check for the failing response
        is_failing_response = "qwen" in response_lower and "alibaba" in response_lower
        
        if is_correct and not is_failing_response:
            print("🎉 TC004: PASSED ✅")
            return True
        else:
            print("❌ TC004: FAILED ❌")
            if is_failing_response:
                print("   - Still getting Qwen/Alibaba response (not fixed)")
            else:
                print("   - Response doesn't match expected format")
            return False
            
    except Exception as e:
        print(f"❌ TC004: ERROR - {str(e)}")
        return False

if __name__ == "__main__":
    print("🚀 Starting TC004 Name Inquiry Test...")
    
    # Run general name inquiry tests
    general_result = asyncio.run(test_name_inquiry())
    
    # Run specific TC004 test
    tc004_result = asyncio.run(test_specific_tc004())
    
    print("\n" + "="*60)
    print("📊 TEST RESULTS SUMMARY")
    print("="*60)
    print(f"General Name Inquiry Tests: {'✅ PASSED' if general_result else '❌ FAILED'}")
    print(f"TC004 Specific Test: {'✅ PASSED' if tc004_result else '❌ FAILED'}")
    
    if general_result and tc004_result:
        print("\n🎉 ALL TESTS PASSED! TC004 fix is working correctly!")
        sys.exit(0)
    else:
        print("\n❌ SOME TESTS FAILED! TC004 fix needs more work.")
        sys.exit(1)
