"""
Medical Record Search Examples

This file contains example usage of all search tools.
Copy and paste these commands to test the search functionality.
"""

# =============================================================================
# SEARCH EXAMPLES FOR HOSPITAL MEDICAL RECORDS
# =============================================================================

# Before running searches, make sure to:
# 1. Set up Qdrant: python setup_qdrant.py
# 2. Process data: python process_hospital_data.py

# =============================================================================
# 1. INTERACTIVE SEARCH (search_medical_records.py)
# =============================================================================

"""
Interactive search with a user-friendly interface:

python search_medical_records.py

This opens an interactive session where you can:
- Type queries and get immediate results
- Use advanced options like --limit and --score
- Get help and statistics
- Exit with 'quit' or 'exit'

Example queries in interactive mode:
- heart attack patient
- teenage suicide attempt
- diabetes medication --limit 10
- chest pain --score 0.5
- help
- stats
- quit
"""

# =============================================================================
# 2. QUICK SEARCH (quick_search.py)
# =============================================================================

"""
Quick one-time searches:

# Search with command line argument:
python quick_search.py "heart attack and chest pain"
python quick_search.py "teenage depression suicide"
python quick_search.py "diabetes patient"

# Run all test queries:
python quick_search.py
"""

# =============================================================================
# 3. ADVANCED SEARCH (advanced_search.py)
# =============================================================================

"""
Advanced search with filters and options:

# Basic search:
python advanced_search.py "heart problems"

# Search with specialty filter:
python advanced_search.py "chest pain" --specialty cardiology

# Search with result limit:
python advanced_search.py "diabetes" --limit 5

# Search with minimum score threshold:
python advanced_search.py "suicide" --min-score 0.6

# Search with age range filter:
python advanced_search.py "patient" --age-min 18 --age-max 65

# Search with medication filter:
python advanced_search.py "patient" --medication "insulin"

# Exclude certain specialties:
python advanced_search.py "patient" --exclude-specialty "psychiatry"

# Search by specific diagnoses:
python advanced_search.py --diagnoses "infarctus" "coronarien"

# Search by symptoms:
python advanced_search.py --symptoms "chest pain" "shortness of breath"

# Find similar cases to a reference file:
python advanced_search.py --similar-to "dataset/data_1.md"

# Export results to JSON:
python advanced_search.py "diabetes" --export "diabetes_results.json"

# Analyze search patterns:
python advanced_search.py --analyze
"""

# =============================================================================
# 4. BATCH SEARCH
# =============================================================================

"""
Batch search for multiple queries:

python search_medical_records.py --batch

This runs predefined test queries and saves results to JSON.
"""

# =============================================================================
# 5. EXAMPLE QUERIES FOR YOUR DATASET
# =============================================================================

# Based on your medical records, here are effective search queries:

CARDIOLOGY_QUERIES = [
    "patient with chest pain and heart attack",
    "syndrome coronarien aigu",
    "douleur thoracique",
    "infarctus myocarde",
    "ECG troponine",
    "cardiac arrest emergency"
]

PSYCHIATRY_QUERIES = [
    "teenage suicide attempt medication overdose",
    "intoxication medicamenteuse volontaire",
    "depression anxiete",
    "tentative suicide",
    "trouble bipolaire",
    "patient psychiatric evaluation"
]

GASTROENTEROLOGY_QUERIES = [
    "abdominal pain appendicitis surgery",
    "douleur abdominale",
    "appendicite aigue",
    "nausee vomissement",
    "fosse iliaque droite",
    "surgical intervention abdomen"
]

ENDOCRINOLOGY_QUERIES = [
    "diabetes patient high blood sugar",
    "diabete type 1",
    "glycemie elevee",
    "acidocetose diabetique",
    "insuline traitement",
    "diabetes medication management"
]

EMERGENCY_QUERIES = [
    "emergency patient SAMU",
    "accident trauma",
    "patient urgence",
    "pompiers ambulance",
    "acute medical condition",
    "emergency department admission"
]

# =============================================================================
# 6. SEARCH TIPS FOR BETTER RESULTS
# =============================================================================

"""
Tips for effective searching:

1. USE MEDICAL TERMINOLOGY:
   - Instead of "heart problem" → "cardiac condition" or "chest pain"
   - Instead of "stomach pain" → "abdominal pain" or "gastric"

2. COMBINE KEYWORDS:
   - "teenage depression suicide" is better than just "depression"
   - "diabetes insulin medication" is better than just "diabetes"

3. USE FRENCH MEDICAL TERMS:
   - "douleur thoracique" (chest pain)
   - "intoxication medicamenteuse" (drug overdose)
   - "syndrome coronarien aigu" (acute coronary syndrome)

4. SPECIFY PATIENT DEMOGRAPHICS:
   - "elderly patient breathing problems"
   - "teenage psychiatric emergency"
   - "adult diabetes management"

5. USE SPECIALTY FILTERS:
   - --specialty cardiology for heart conditions
   - --specialty psychiatry for mental health
   - --specialty emergency for acute cases

6. ADJUST SCORE THRESHOLDS:
   - --min-score 0.7 for very relevant results
   - --min-score 0.3 for broader search
   - --min-score 0.5 for balanced results

7. EXPORT RESULTS FOR ANALYSIS:
   - --export "results.json" to save findings
   - Use JSON exports for chatbot integration
"""

# =============================================================================
# 7. INTEGRATION WITH CHATBOT
# =============================================================================

"""
To integrate with your chatbot, use this pattern:

from medical_record_embedder import MedicalRecordEmbedder

class ChatbotIntegration:
    def __init__(self):
        self.embedder = MedicalRecordEmbedder()
        self.embedder.setup_qdrant()
    
    def get_medical_context(self, user_query):
        # First check SQL database
        sql_results = self.check_sql_database(user_query)
        
        if not sql_results:
            # Query medical records if no SQL results
            vector_results = self.embedder.query_similar_records(
                user_query, 
                limit=5
            )
            
            # Filter by relevance
            relevant_results = [
                r for r in vector_results 
                if r['score'] > 0.6
            ]
            
            return self.format_medical_context(relevant_results)
        
        return sql_results
    
    def format_medical_context(self, results):
        context = []
        for result in results:
            record = result['record']
            context.append({
                'diagnosis': record['primary_diagnosis'],
                'specialty': record['record_type'],
                'medications': record['medications'],
                'relevance_score': result['score']
            })
        return context
"""

# =============================================================================
# 8. TROUBLESHOOTING
# =============================================================================

"""
Common issues and solutions:

1. "Error initializing": 
   - Make sure Qdrant is running: docker ps | grep qdrant
   - Start Qdrant: docker start qdrant

2. "No results found":
   - Check if data is processed: python process_hospital_data.py
   - Lower minimum score: --min-score 0.2
   - Try broader search terms

3. "Connection refused":
   - Qdrant not running: python setup_qdrant.py
   - Check Qdrant port: http://localhost:6333/dashboard

4. "Slow searches":
   - Reduce limit: --limit 5
   - Increase minimum score: --min-score 0.5

5. "Memory issues":
   - Process data in smaller batches
   - Reduce embedding dimensions in config
"""

if __name__ == "__main__":
    print("📚 Medical Record Search Examples")
    print("=" * 50)
    print("This file contains examples and documentation.")
    print("Copy the commands above to test the search functionality.")
    print("\n🚀 Quick start:")
    print("1. python setup_qdrant.py")
    print("2. python process_hospital_data.py")
    print("3. python search_medical_records.py")
    print("\n📖 See the examples above for detailed usage!")
