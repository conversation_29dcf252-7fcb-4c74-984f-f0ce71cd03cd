#!/usr/bin/env python3
"""
Hospital Data Sync Script

This script syncs data from the hospital database to the app database:
1. Creates departments from doctor specialties (Medecins.Specialite with deduplication)
2. Creates hospital-specific roles (admin, head_of_department, medical_staff)
3. Creates users from doctors with format {lastname}{firstname[0]}{number}
4. Creates an admin user

Note: medical_staff role is used for both doctors and nurses (combined role)

Usage: python scripts/sync_hospital_data.py
"""

import os
import sys
import logging
import psycopg2
import psycopg2.extras
from psycopg2 import pool
from datetime import datetime
from passlib.context import CryptContext
import re

# Add backend directory to path to import config
backend_dir = os.path.join(os.path.dirname(__file__), '..', 'backend')
sys.path.insert(0, backend_dir)

from config import Config

# ============================================================================
# DATABASE CONFIGURATION - MODIFY THESE FOR YOUR SETUP
# ============================================================================

# Option 1: Use separate databases
USE_SEPARATE_DATABASES = True

if USE_SEPARATE_DATABASES:
    # Hospital database connection - UPDATE THESE WITH YOUR ACTUAL DATABASE DETAILS
    HOSPITAL_DSN = "postgresql://postgres.ixajpflnkexezmsxyofd:<EMAIL>:5432/postgres"

    # App database connection - UPDATE THESE WITH YOUR ACTUAL DATABASE DETAILS
    APP_DSN = "postgresql://postgres.oizrmaqixykhjftegmtv:<EMAIL>:6543/postgres"

    # Example with your Supabase setup:
    # HOSPITAL_DSN = "postgresql://postgres:<EMAIL>:5432/postgres"
    # APP_DSN = "postgresql://postgres:<EMAIL>:5432/postgres"
else:
    # Use same database for both (from config.py)
    HOSPITAL_DSN = None
    APP_DSN = None

# ============================================================================

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('scripts/sync_log.txt')
    ]
)
logger = logging.getLogger(__name__)

# Password hashing setup (consistent with auth system)
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

class DatabaseSyncer:
    def __init__(self, hospital_dsn=None, app_dsn=None):
        self.config = Config()
        self.hospital_pool = None
        self.app_pool = None

        # Use provided DSNs or build from config
        if hospital_dsn:
            self.hospital_dsn = hospital_dsn
        else:
            # Use hospital database config
            db_config = self.config.hospital_database_config
            self.hospital_dsn = f"postgresql://{db_config['user']}:{db_config['password']}@{db_config['host']}:{db_config['port']}/{db_config['dbname']}"

        if app_dsn:
            self.app_dsn = app_dsn
        else:
            # Use app database config (not hospital database)
            db_config = self.config.app_database_config
            self.app_dsn = f"postgresql://{db_config['user']}:{db_config['password']}@{db_config['host']}:{db_config['port']}/{db_config['dbname']}"

    def connect_to_databases(self):
        """Create connection pools for both hospital and app databases"""
        try:
            # Debug: Print connection details (without passwords)
            logger.info(f"Hospital DB: {self.hospital_dsn.split('@')[1] if '@' in self.hospital_dsn else 'Unknown'}")
            logger.info(f"App DB: {self.app_dsn.split('@')[1] if '@' in self.app_dsn else 'Unknown'}")

            # Create connection pools
            self.hospital_pool = psycopg2.pool.SimpleConnectionPool(
                minconn=1,
                maxconn=5,
                dsn=self.hospital_dsn
            )

            self.app_pool = psycopg2.pool.SimpleConnectionPool(
                minconn=1,
                maxconn=5,
                dsn=self.app_dsn
            )

            logger.info("Successfully created connection pools for both hospital and app databases")

        except Exception as e:
            logger.error(f"Failed to create connection pools: {e}")
            raise

    def get_hospital_connection(self):
        """Get a connection from the hospital pool"""
        return self.hospital_pool.getconn()

    def get_app_connection(self):
        """Get a connection from the app pool"""
        return self.app_pool.getconn()

    def return_hospital_connection(self, conn):
        """Return a connection to the hospital pool"""
        self.hospital_pool.putconn(conn)

    def return_app_connection(self, conn):
        """Return a connection to the app pool"""
        self.app_pool.putconn(conn)

    def close_connections(self):
        """Close all connection pools"""
        if self.hospital_pool:
            self.hospital_pool.closeall()
        if self.app_pool:
            self.app_pool.closeall()
        logger.info("All connection pools closed")
    
    def generate_username(self, last_name, first_name, existing_usernames):
        """Generate unique username in format {main_lastname}{other_parts_initials}{firstname[0]}{number}"""
        # Clean and normalize names
        first_name = re.sub(r'[^a-zA-Z]', '', first_name or '').lower()
        
        if not last_name:
            last_name = 'user'
        
        # Split last name into parts (handles French naming conventions)
        last_name_parts = last_name.lower().split()
        
        # French particles and common prefixes that should be abbreviated
        particles = {'de', 'du', 'des', 'van', 'von', 'da', 'di', 'della', 'del', 'la', 'le', 'les'}
        
        main_surname = ""
        other_parts = []
        
        # Find the main surname (usually the longest single word that's not a particle)
        for part in last_name_parts:
            clean_part = re.sub(r'[^a-zA-Z]', '', part)
            if clean_part:
                if clean_part.lower() in particles:
                    other_parts.append(clean_part[0])  # Take first char of particle
                elif len(clean_part) > len(main_surname) and clean_part.lower() not in particles:
                    # If we already had a main surname, move it to other parts
                    if main_surname:
                        other_parts.append(main_surname[0])
                    main_surname = clean_part
                else:
                    other_parts.append(clean_part[0])  # Take first char of other parts
        
        # If no main surname found, use the last part
        if not main_surname and last_name_parts:
            main_surname = re.sub(r'[^a-zA-Z]', '', last_name_parts[-1])
        
        # If still no main surname, default
        if not main_surname:
            main_surname = 'user'
        
        # Build username components
        first_char = first_name[0] if first_name else 'x'
        other_initials = ''.join(other_parts)
        base_username = f"{main_surname}{other_initials}{first_char}"
        
        # Find unique username
        counter = 1
        username = f"{base_username}{counter}"
        while username in existing_usernames:
            counter += 1
            username = f"{base_username}{counter}"
        
        existing_usernames.add(username)
        return username
    
    def hash_password(self, password):
        """Hash password using bcrypt"""
        return pwd_context.hash(password)
    
    def sync_departments(self):
        """Create departments from doctor specialties"""
        logger.info("Syncing departments...")

        hospital_conn = None
        app_conn = None

        try:
            # Get connection from hospital pool
            hospital_conn = self.get_hospital_connection()

            # Get unique specialties from hospital database
            with hospital_conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cursor:
                cursor.execute("""
                    SELECT DISTINCT Specialite
                    FROM Medecins
                    WHERE Specialite IS NOT NULL AND Specialite != ''
                    ORDER BY Specialite
                """)
                specialties = cursor.fetchall()

            # Get connection from app pool
            app_conn = self.get_app_connection()
            app_conn.autocommit = False

            # Insert departments into app database
            with app_conn.cursor() as cursor:
                # Add specialty departments
                departments = []
                for specialty in specialties:
                    departments.append((specialty['specialite'], f"Department for {specialty['specialite']} specialists"))

                for name, description in departments:
                    cursor.execute("""
                        INSERT INTO department (name, description)
                        VALUES (%s, %s)
                        ON CONFLICT DO NOTHING
                    """, (name, description))

                app_conn.commit()
                logger.info(f"Created {len(departments)} departments")

        except Exception as e:
            if app_conn:
                app_conn.rollback()
            logger.error(f"Error syncing departments: {e}")
            raise
        finally:
            # Return connections to pools
            if hospital_conn:
                self.return_hospital_connection(hospital_conn)
            if app_conn:
                self.return_app_connection(app_conn)
    
    def sync_roles(self):
        """Create basic roles"""
        logger.info("Syncing roles...")

        app_conn = None

        try:
            # Get connection from app pool
            app_conn = self.get_app_connection()
            app_conn.autocommit = False

            with app_conn.cursor() as cursor:
                # Create roles: admin, head of department, medical staff (doctor and nurse combined)
                roles = [
                    ('admin', 'System Administrator - Full access to all features'),
                    ('head_of_department', 'Head of Department - Departmental oversight and management'),
                    ('medical_staff', 'Doctor and Nurse - Medical staff with patient data access')
                ]

                for name, description in roles:
                    cursor.execute("""
                        INSERT INTO role (name, description)
                        VALUES (%s, %s)
                        ON CONFLICT DO NOTHING
                    """, (name, description))

                app_conn.commit()
                logger.info(f"Created {len(roles)} roles")

        except Exception as e:
            if app_conn:
                app_conn.rollback()
            logger.error(f"Error syncing roles: {e}")
            raise
        finally:
            # Return connection to pool
            if app_conn:
                self.return_app_connection(app_conn)
    


    def sync_users_from_doctors(self):
        """Create users from doctors"""
        logger.info("Syncing users from doctors...")

        hospital_conn = None
        app_conn = None

        try:
            # Get connection from app pool for existing usernames
            app_conn = self.get_app_connection()
            app_conn.autocommit = False

            # Get existing usernames
            with app_conn.cursor() as cursor:
                cursor.execute("SELECT username FROM \"user\"")
                existing_usernames = {row[0] for row in cursor.fetchall()}

            # Get connection from hospital pool
            hospital_conn = self.get_hospital_connection()

            # Get doctors from hospital database
            with hospital_conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cursor:
                cursor.execute("""
                    SELECT ID_medecin, Nom, Prenom, Specialite, Email
                    FROM Medecins
                    ORDER BY Nom, Prenom
                """)
                doctors = cursor.fetchall()

            # Create users for doctors
            with app_conn.cursor() as cursor:
                users_created = 0
                for doctor in doctors:
                    username = self.generate_username(
                        doctor['nom'],
                        doctor['prenom'],
                        existing_usernames
                    )

                    # Get department ID for specialty
                    cursor.execute("""
                        SELECT id FROM department WHERE name = %s
                    """, (doctor['specialite'],))
                    dept_result = cursor.fetchone()
                    department_id = dept_result[0] if dept_result else None

                    # Get medical staff role ID (for both doctors and nurses)
                    cursor.execute("SELECT id FROM role WHERE name = 'medical_staff'")
                    role_id = cursor.fetchone()[0]

                    # Create user
                    password_hash = self.hash_password('doctor123')  # Default password

                    cursor.execute("""
                        INSERT INTO "user" (
                            username, first_name, last_name, password_hash,
                            department_id, role_id, is_active, created_at, updated_at
                        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
                        ON CONFLICT (username) DO NOTHING
                    """, (
                        username,
                        doctor['prenom'] or '',
                        doctor['nom'] or '',
                        password_hash,
                        department_id,
                        role_id,
                        True,
                        datetime.now(),
                        datetime.now()
                    ))

                    if cursor.rowcount > 0:
                        users_created += 1

                app_conn.commit()
                logger.info(f"Created {users_created} doctor users")

        except Exception as e:
            if app_conn:
                app_conn.rollback()
            logger.error(f"Error syncing doctor users: {e}")
            raise
        finally:
            # Return connections to pools
            if hospital_conn:
                self.return_hospital_connection(hospital_conn)
            if app_conn:
                self.return_app_connection(app_conn)



    def create_admin_user(self):
        """Create admin user"""
        logger.info("Creating admin user...")

        app_conn = None

        try:
            # Get connection from app pool
            app_conn = self.get_app_connection()
            app_conn.autocommit = False

            with app_conn.cursor() as cursor:
                # Check if admin user already exists
                cursor.execute("SELECT id FROM \"user\" WHERE username = 'admin'")
                if cursor.fetchone():
                    logger.info("Admin user already exists, skipping creation")
                    return

                # Get admin role ID
                cursor.execute("SELECT id FROM role WHERE name = 'admin'")
                admin_role_id = cursor.fetchone()[0]

                # Get a department ID (use first available)
                cursor.execute("SELECT id FROM department LIMIT 1")
                dept_id = cursor.fetchone()[0]

                # Create admin user
                password_hash = self.hash_password('admin123')  # Default admin password

                cursor.execute("""
                    INSERT INTO "user" (
                        username, first_name, last_name, password_hash,
                        department_id, role_id, is_active, created_at, updated_at
                    ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
                """, (
                    'admin',
                    'System',
                    'Administrator',
                    password_hash,
                    dept_id,
                    admin_role_id,
                    True,
                    datetime.now(),
                    datetime.now()
                ))

                app_conn.commit()
                logger.info("Admin user created successfully")

        except Exception as e:
            if app_conn:
                app_conn.rollback()
            logger.error(f"Error creating admin user: {e}")
            raise
        finally:
            # Return connection to pool
            if app_conn:
                self.return_app_connection(app_conn)

    def run_sync(self):
        """Run the complete sync process"""
        logger.info("Starting hospital data sync...")
        start_time = datetime.now()

        try:
            self.connect_to_databases()

            # Run sync operations in order
            self.sync_departments()
            self.sync_roles()
            self.sync_users_from_doctors()
            self.create_admin_user()

            end_time = datetime.now()
            duration = end_time - start_time
            logger.info(f"Sync completed successfully in {duration.total_seconds():.2f} seconds")

        except Exception as e:
            logger.error(f"Sync failed: {e}")
            raise
        finally:
            self.close_connections()


def main():
    """Main function"""
    try:
        # Create syncer with configured database connections
        syncer = DatabaseSyncer(hospital_dsn=HOSPITAL_DSN, app_dsn=APP_DSN)
        syncer.run_sync()

        print("\n" + "="*50)
        print("SYNC COMPLETED SUCCESSFULLY!")
        print("="*50)
        print("\nRoles created:")
        print("- admin: System Administrator - Full access to all features")
        print("- head_of_department: Head of Department - Departmental oversight and management")
        print("- medical_staff: Doctor and Nurse - Medical staff with patient data access")
        print("\nDefault passwords created:")
        print("- Admin user: username='admin', password='admin123'")
        print("- Medical staff users: password='doctor123'")
        print("\nPlease change these default passwords in production!")
        print("="*50)

    except Exception as e:
        print(f"\nSync failed: {e}")
        logger.error(f"Sync failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
