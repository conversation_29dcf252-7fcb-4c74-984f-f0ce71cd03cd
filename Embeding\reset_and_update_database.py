"""
Reset and Update Vector Database Utility

This script provides functionality to:
1. Clear existing data from Qdrant vector database
2. Reload and process medical records
3. Create fresh embeddings
4. Store updated data in the database
"""

import logging
from pathlib import Path
from medical_record_embedder import MedicalRecordEmbedder
from qdrant_client import Qdrant<PERSON>lient
from qdrant_client.models import Distance, VectorParams
import sys

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DatabaseResetManager:
    """Manager for resetting and updating the vector database"""
    
    def __init__(self, host: str = "localhost", port: int = 6333, collection_name: str = "medical_records"):
        """
        Initialize the database reset manager
        
        Args:
            host: Qdrant host
            port: Qdrant port  
            collection_name: Name of the collection to manage
        """
        self.host = host
        self.port = port
        self.collection_name = collection_name
        self.client = None
        
    def connect_to_qdrant(self):
        """Connect to Qdrant database"""
        try:
            self.client = QdrantClient(host=self.host, port=self.port)
            logger.info(f"Connected to Qdrant at {self.host}:{self.port}")
            return True
        except Exception as e:
            logger.error(f"Failed to connect to Qdrant: {e}")
            return False
    
    def check_collection_exists(self) -> bool:
        """Check if the collection exists"""
        try:
            collections = self.client.get_collections()
            collection_names = [col.name for col in collections.collections]
            exists = self.collection_name in collection_names
            logger.info(f"Collection '{self.collection_name}' exists: {exists}")
            return exists
        except Exception as e:
            logger.error(f"Error checking collection existence: {e}")
            return False
    
    def delete_collection(self):
        """Delete the existing collection"""
        try:
            if self.check_collection_exists():
                self.client.delete_collection(collection_name=self.collection_name)
                logger.info(f"Deleted collection: {self.collection_name}")
            else:
                logger.info(f"Collection {self.collection_name} does not exist, nothing to delete")
        except Exception as e:
            logger.error(f"Error deleting collection: {e}")
            raise
    
    def create_fresh_collection(self, vector_size: int = 384):
        """Create a fresh collection"""
        try:
            self.client.create_collection(
                collection_name=self.collection_name,
                vectors_config=VectorParams(
                    size=vector_size,
                    distance=Distance.COSINE
                )
            )
            logger.info(f"Created fresh collection: {self.collection_name}")
        except Exception as e:
            logger.error(f"Error creating collection: {e}")
            raise
    
    def get_collection_info(self):
        """Get information about the collection"""
        try:
            if self.check_collection_exists():
                info = self.client.get_collection(collection_name=self.collection_name)
                count = self.client.count(collection_name=self.collection_name)
                logger.info(f"Collection '{self.collection_name}' info:")
                logger.info(f"  - Vector size: {info.config.params.vectors.size}")
                logger.info(f"  - Distance metric: {info.config.params.vectors.distance}")
                logger.info(f"  - Total points: {count.count}")
                return info, count
            else:
                logger.info(f"Collection '{self.collection_name}' does not exist")
                return None, None
        except Exception as e:
            logger.error(f"Error getting collection info: {e}")
            return None, None
    
    def clear_collection_data(self):
        """Clear all data from the collection without deleting the collection"""
        try:
            if self.check_collection_exists():
                # Get all point IDs
                points, _ = self.client.scroll(
                    collection_name=self.collection_name,
                    limit=10000,  # Adjust as needed
                    with_payload=False,
                    with_vectors=False
                )
                
                if points:
                    point_ids = [point.id for point in points]
                    self.client.delete(
                        collection_name=self.collection_name,
                        points_selector=point_ids
                    )
                    logger.info(f"Cleared {len(point_ids)} points from collection")
                else:
                    logger.info("Collection is already empty")
            else:
                logger.info(f"Collection {self.collection_name} does not exist")
        except Exception as e:
            logger.error(f"Error clearing collection data: {e}")
            raise

def reset_and_update_database(
    dataset_path: str = "d:/Chatbot_hospital/dataset",
    model_name: str = "sentence-transformers/all-MiniLM-L6-v2",
    enable_chunking: bool = True,
    max_chunk_size: int = 300,
    reset_method: str = "recreate"  # "recreate" or "clear"
):
    """
    Complete reset and update of the vector database
    
    Args:
        dataset_path: Path to the medical records dataset
        model_name: Sentence transformer model name
        enable_chunking: Whether to enable chunking
        max_chunk_size: Maximum chunk size in words
        reset_method: "recreate" (delete and recreate collection) or "clear" (clear data only)
    """
    logger.info("=== Starting Database Reset and Update ===")
    
    # Step 1: Initialize components
    logger.info("Step 1: Initializing components...")
    db_manager = DatabaseResetManager()
    embedder = MedicalRecordEmbedder(
        model_name=model_name,
        enable_chunking=enable_chunking,
        max_chunk_size=max_chunk_size
    )
    
    # Step 2: Connect to database
    logger.info("Step 2: Connecting to database...")
    if not db_manager.connect_to_qdrant():
        logger.error("Failed to connect to Qdrant. Make sure it's running.")
        return False
    
    # Step 3: Show current state
    logger.info("Step 3: Current database state...")
    db_manager.get_collection_info()
    
    # Step 4: Reset database
    logger.info(f"Step 4: Resetting database using method: {reset_method}")
    try:
        if reset_method == "recreate":
            # Delete and recreate collection
            db_manager.delete_collection()
            db_manager.create_fresh_collection(vector_size=embedder.model.get_sentence_embedding_dimension())
        elif reset_method == "clear":
            # Clear data only
            db_manager.clear_collection_data()
        else:
            logger.error(f"Invalid reset method: {reset_method}")
            return False
            
        logger.info("Database reset completed successfully")
    except Exception as e:
        logger.error(f"Database reset failed: {e}")
        return False
    
    # Step 5: Process dataset
    logger.info("Step 5: Processing medical records dataset...")
    try:
        # Setup embedder with Qdrant
        embedder.qdrant_client = db_manager.client
        embedder.collection_name = db_manager.collection_name
        
        # Process records
        records = embedder.process_dataset(dataset_path)
        if not records:
            logger.error("No records found or processed")
            return False
            
        logger.info(f"Successfully processed {len(records)} medical records")
    except Exception as e:
        logger.error(f"Failed to process dataset: {e}")
        return False
    
    # Step 6: Create embeddings
    logger.info("Step 6: Creating embeddings...")
    try:
        embeddings = embedder.create_embeddings(records)
        logger.info(f"Created {len(embeddings)} embeddings")
    except Exception as e:
        logger.error(f"Failed to create embeddings: {e}")
        return False
    
    # Step 7: Store in database
    logger.info("Step 7: Storing embeddings in database...")
    try:
        embedder.store_in_qdrant(embeddings)
        logger.info("Successfully stored embeddings in database")
    except Exception as e:
        logger.error(f"Failed to store embeddings: {e}")
        return False
    
    # Step 8: Verify final state
    logger.info("Step 8: Verifying final database state...")
    info, count = db_manager.get_collection_info()
    
    if count and count.count > 0:
        logger.info(f"✅ Database update completed successfully!")
        logger.info(f"✅ Total embeddings stored: {count.count}")
        
        # Test a sample query
        logger.info("Step 9: Testing sample query...")
        try:
            test_results = embedder.query_similar_records("heart problems", limit=3)
            logger.info(f"✅ Sample query returned {len(test_results)} results")
            for i, result in enumerate(test_results[:2]):
                file_name = Path(result['file_path']).name
                logger.info(f"  {i+1}. {result['summary']} - {file_name} (Score: {result['score']:.3f})")
        except Exception as e:
            logger.warning(f"Sample query failed: {e}")
        
        return True
    else:
        logger.error("❌ Database appears to be empty after update")
        return False

def interactive_reset():
    """Interactive mode for database reset"""
    print("=== Interactive Database Reset and Update ===\n")
    
    # Get user preferences
    print("Choose reset method:")
    print("1. Recreate collection (delete and recreate)")
    print("2. Clear data only (keep collection structure)")
    choice = input("Enter choice (1 or 2) [default: 1]: ").strip() or "1"
    
    reset_method = "recreate" if choice == "1" else "clear"
    
    enable_chunking = input("Enable chunking? (y/n) [default: y]: ").strip().lower()
    enable_chunking = enable_chunking != "n"
    
    if enable_chunking:
        chunk_size = input("Chunk size (words) [default: 300]: ").strip()
        try:
            chunk_size = int(chunk_size) if chunk_size else 300
        except ValueError:
            chunk_size = 300
    else:
        chunk_size = 300
    
    dataset_path = input("Dataset path [default: d:/Chatbot_hospital/dataset]: ").strip()
    dataset_path = dataset_path or "d:/Chatbot_hospital/dataset"
    
    print(f"\nConfiguration:")
    print(f"  - Reset method: {reset_method}")
    print(f"  - Enable chunking: {enable_chunking}")
    print(f"  - Chunk size: {chunk_size}")
    print(f"  - Dataset path: {dataset_path}")
    
    confirm = input("\nProceed with reset? (y/n): ").strip().lower()
    if confirm == "y":
        success = reset_and_update_database(
            dataset_path=dataset_path,
            enable_chunking=enable_chunking,
            max_chunk_size=chunk_size,
            reset_method=reset_method
        )
        
        if success:
            print("\n✅ Database reset and update completed successfully!")
        else:
            print("\n❌ Database reset and update failed!")
    else:
        print("Operation cancelled.")

def main():
    """Main function"""
    if len(sys.argv) > 1:
        if sys.argv[1] == "interactive":
            interactive_reset()
        elif sys.argv[1] == "info":
            # Just show database info
            db_manager = DatabaseResetManager()
            if db_manager.connect_to_qdrant():
                db_manager.get_collection_info()
        elif sys.argv[1] == "clear":
            # Quick clear
            success = reset_and_update_database(reset_method="clear")
            print("✅ Success!" if success else "❌ Failed!")
        elif sys.argv[1] == "recreate":
            # Quick recreate
            success = reset_and_update_database(reset_method="recreate")
            print("✅ Success!" if success else "❌ Failed!")
        else:
            print("Usage:")
            print("  python reset_and_update_database.py interactive")
            print("  python reset_and_update_database.py info")
            print("  python reset_and_update_database.py clear")
            print("  python reset_and_update_database.py recreate")
    else:
        # Default: recreate with chunking
        success = reset_and_update_database(reset_method="recreate")
        print("✅ Success!" if success else "❌ Failed!")

if __name__ == "__main__":
    main()
