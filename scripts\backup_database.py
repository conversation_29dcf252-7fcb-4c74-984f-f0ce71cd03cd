#!/usr/bin/env python3
"""
Database Backup Script
=====================

This script creates a backup of the current database before migration.
"""

import sys
import os
import subprocess
import logging
from datetime import datetime

# Add backend directory to path to import config
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'backend'))

try:
    from config import Config
except ImportError:
    print("Error: Could not import config.py. Make sure you're running this from the project root.")
    sys.exit(1)

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DatabaseBackup:
    def __init__(self):
        self.config = Config()
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
    def create_backup(self):
        """Create a database backup using pg_dump"""
        db_config = self.config.app_database_config
        backup_filename = f"app_db_backup_{self.timestamp}.sql"
        backup_path = os.path.join(os.path.dirname(__file__), '..', 'backups', backup_filename)
        
        # Create backups directory if it doesn't exist
        os.makedirs(os.path.dirname(backup_path), exist_ok=True)
        
        # Construct pg_dump command
        cmd = [
            'pg_dump',
            f"--host={db_config['host']}",
            f"--port={db_config['port']}",
            f"--username={db_config['user']}",
            f"--dbname={db_config['dbname']}",
            '--verbose',
            '--clean',
            '--if-exists',
            '--create',
            '--format=plain',
            f"--file={backup_path}"
        ]
        
        # Set password environment variable
        env = os.environ.copy()
        env['PGPASSWORD'] = db_config['password']
        
        try:
            logger.info(f"Creating backup: {backup_filename}")
            result = subprocess.run(cmd, env=env, capture_output=True, text=True)
            
            if result.returncode == 0:
                logger.info(f"✅ Backup created successfully: {backup_path}")
                return backup_path
            else:
                logger.error(f"❌ Backup failed: {result.stderr}")
                return None
                
        except FileNotFoundError:
            logger.error("❌ pg_dump not found. Please install PostgreSQL client tools.")
            return None
        except Exception as e:
            logger.error(f"❌ Backup failed: {e}")
            return None

def main():
    """Main backup function"""
    print("📦 Creating database backup...")
    
    backup = DatabaseBackup()
    backup_path = backup.create_backup()
    
    if backup_path:
        print(f"✅ Backup created: {backup_path}")
        return True
    else:
        print("❌ Backup failed!")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
