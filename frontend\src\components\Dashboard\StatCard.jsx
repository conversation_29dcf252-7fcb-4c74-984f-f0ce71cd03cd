import React from 'react';

const StatCard = ({ title, value, icon, color = 'blue', onClick, clickable = false, isHidden = false }) => {
  const colorClasses = {
    blue: 'text-blue-600 dark:text-blue-400',
    green: 'text-green-600 dark:text-green-400',
    purple: 'text-purple-600 dark:text-purple-400',
    orange: 'text-orange-600 dark:text-orange-400',
    red: 'text-red-600 dark:text-red-400',
    gray: 'text-gray-600 dark:text-gray-400'
  };

  const baseClasses = "bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6 hover:shadow-lg transition-all duration-300 hover:-translate-y-1";
  const clickableClasses = clickable ? "cursor-pointer hover:border-blue-300 dark:hover:border-blue-600" : "";
  const hiddenClasses = isHidden ? "opacity-0 scale-95 pointer-events-none" : "opacity-100 scale-100";

  const cardContent = (
    <div className="flex items-center justify-between">
      <div>
        <p className="text-sm font-medium text-gray-600 dark:text-gray-400 uppercase tracking-wide">
          {title}
        </p>
        <p className={`text-3xl font-bold mt-2 ${colorClasses[color]}`}>
          {value}
        </p>
      </div>
      <div className={`text-3xl ${colorClasses[color]} opacity-80`}>
        <i className={icon}></i>
      </div>
    </div>
  );

  const handleClick = (e) => {
    if (onClick) {
      const rect = e.currentTarget.getBoundingClientRect();
      const centerX = rect.left + rect.width / 2;
      const centerY = rect.top + rect.height / 2;

      onClick({
        x: centerX,
        y: centerY,
        element: e.currentTarget,
        rect: rect
      });
    }
  };

  if (clickable && onClick) {
    return (
      <div
        className={`${baseClasses} ${clickableClasses} ${hiddenClasses}`}
        onClick={handleClick}
        role="button"
        tabIndex={0}
        onKeyDown={(e) => {
          if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault();
            handleClick(e);
          }
        }}
      >
        {cardContent}
      </div>
    );
  }

  return (
    <div className={`${baseClasses} ${hiddenClasses}`}>
      {cardContent}
    </div>
  );
};

export default StatCard;
