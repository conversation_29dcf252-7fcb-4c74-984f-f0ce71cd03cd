#!/usr/bin/env python3
"""
Database Purge and Migration Script
===================================

This script will:
1. Purge the existing app database completely
2. Recreate the schema from create_app_db.sql
3. Add enhancements for linear chat architecture and chart optimization
4. Populate with initial seed data

Note: This script uses credentials from config.py to perform destructive actions
since Supabase MCP only provides read access.
"""

import sys
import os
import psycopg2
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT
import logging
from datetime import datetime

# Add backend directory to path to import config
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'backend'))

try:
    from config import Config
except ImportError:
    print("Error: Could not import config.py. Make sure you're running this from the project root.")
    sys.exit(1)

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'database_migration_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class DatabaseMigrator:
    def __init__(self):
        self.config = Config()
        self.connection = None
        
    def connect(self):
        """Connect to the app database using credentials from config.py"""
        try:
            db_config = self.config.app_database_config
            logger.info(f"Connecting to database: {db_config['host']}:{db_config['port']}/{db_config['dbname']}")
            
            self.connection = psycopg2.connect(
                host=db_config['host'],
                database=db_config['dbname'],
                user=db_config['user'],
                password=db_config['password'],
                port=db_config['port']
            )
            self.connection.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
            logger.info("Successfully connected to database")
            return True
            
        except Exception as e:
            logger.error(f"Failed to connect to database: {e}")
            return False
    
    def disconnect(self):
        """Close database connection"""
        if self.connection:
            self.connection.close()
            logger.info("Database connection closed")
    
    def execute_sql(self, sql, description="SQL execution"):
        """Execute SQL with error handling and logging"""
        try:
            cursor = self.connection.cursor()
            logger.info(f"Executing: {description}")
            cursor.execute(sql)
            cursor.close()
            logger.info(f"Successfully completed: {description}")
            return True
        except Exception as e:
            logger.error(f"Failed {description}: {e}")
            return False
    
    def purge_database(self):
        """Completely purge all tables and data from the app database"""
        logger.warning("🚨 STARTING DATABASE PURGE - ALL DATA WILL BE LOST! 🚨")
        
        # Get all tables to drop
        get_tables_sql = """
        SELECT tablename FROM pg_tables 
        WHERE schemaname = 'public' 
        AND tablename NOT LIKE 'pg_%' 
        AND tablename != 'information_schema'
        """
        
        try:
            cursor = self.connection.cursor()
            cursor.execute(get_tables_sql)
            tables = [row[0] for row in cursor.fetchall()]
            cursor.close()
            
            if not tables:
                logger.info("No tables found to drop")
                return True
                
            logger.info(f"Found {len(tables)} tables to drop: {', '.join(tables)}")
            
            # Drop all tables with CASCADE to handle dependencies
            for table in tables:
                # Quote table name to handle reserved keywords like 'user'
                quoted_table = f'"{table}"' if table == 'user' else table
                drop_sql = f"DROP TABLE IF EXISTS {quoted_table} CASCADE"
                if not self.execute_sql(drop_sql, f"Dropping table {table}"):
                    return False
            
            # Drop all sequences
            drop_sequences_sql = """
            DO $$ 
            DECLARE 
                seq_name TEXT;
            BEGIN 
                FOR seq_name IN 
                    SELECT sequence_name FROM information_schema.sequences 
                    WHERE sequence_schema = 'public'
                LOOP 
                    EXECUTE 'DROP SEQUENCE IF EXISTS ' || seq_name || ' CASCADE';
                END LOOP; 
            END $$;
            """
            
            if not self.execute_sql(drop_sequences_sql, "Dropping all sequences"):
                return False
                
            logger.info("✅ Database purge completed successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to purge database: {e}")
            return False
    
    def create_base_schema(self):
        """Create the base schema from create_app_db.sql"""
        logger.info("Creating base schema from create_app_db.sql")
        
        # Read the SQL file
        sql_file_path = os.path.join(os.path.dirname(__file__), '..', 'sql', 'create_app_db.sql')
        
        try:
            with open(sql_file_path, 'r', encoding='utf-8') as f:
                sql_content = f.read()
            
            # Execute the SQL content
            if not self.execute_sql(sql_content, "Creating base schema"):
                return False
                
            logger.info("✅ Base schema created successfully")
            return True
            
        except FileNotFoundError:
            logger.error(f"SQL file not found: {sql_file_path}")
            return False
        except Exception as e:
            logger.error(f"Failed to create base schema: {e}")
            return False
    
    def add_linear_chat_enhancements(self):
        """Add enhancements for linear chat architecture"""
        logger.info("Adding linear chat architecture enhancements")
        
        enhancements_sql = """
        -- Add title and metadata to conversation table for enhanced session management
        ALTER TABLE conversation 
        ADD COLUMN IF NOT EXISTS title TEXT DEFAULT 'New Conversation',
        ADD COLUMN IF NOT EXISTS metadata JSONB DEFAULT '{}';
        
        -- Add constraint to ensure linear conversation flow
        -- Prevent multiple main assistant responses to the same user message
        CREATE UNIQUE INDEX IF NOT EXISTS idx_linear_conversation_flow 
        ON chat_history (conversation_id, message_type, created_at)
        WHERE message_type = 'assistant' AND parent_message_id IS NULL;
        
        -- Update parent_message_id column comment for clarity
        COMMENT ON COLUMN chat_history.parent_message_id IS 
        'Links related messages within the same response cycle (e.g., intermediate SQL steps to main query). 
        NOT used for alternative responses - ensures linear conversation flow. 
        Only intermediate steps should reference a parent message.';
        
        -- Add table comment for linear chat architecture
        COMMENT ON TABLE chat_history IS 
        'Chat history table with enforced linear conversation flow. 
        Each user message receives exactly one assistant response thread.
        Intermediate steps are linked via parent_message_id but do not create alternative responses.
        Supports rich message types: text, sql, chart, thinking, intermediate_sql, error.';
        
        -- Add indexes for enhanced conversation management
        CREATE INDEX IF NOT EXISTS idx_conversation_title ON conversation(title);
        CREATE INDEX IF NOT EXISTS idx_conversation_metadata_gin ON conversation USING gin(metadata);
        """
        
        return self.execute_sql(enhancements_sql, "Adding linear chat enhancements")
    
    def add_analytics_tables(self):
        """Add tables for user interaction analytics dashboard"""
        logger.info("Adding analytics tables for dashboard")
        
        analytics_sql = """
        -- User engagement metrics table for dashboard analytics
        CREATE TABLE IF NOT EXISTS user_engagement_metrics (
            id SERIAL PRIMARY KEY,
            user_id INTEGER REFERENCES "user"(id),
            date DATE NOT NULL,
            total_conversations INTEGER DEFAULT 0,
            total_messages INTEGER DEFAULT 0,
            avg_session_duration_minutes DECIMAL(10,2),
            questions_asked INTEGER DEFAULT 0,
            sql_queries_generated INTEGER DEFAULT 0,
            charts_created INTEGER DEFAULT 0,
            positive_feedback_count INTEGER DEFAULT 0,
            negative_feedback_count INTEGER DEFAULT 0,
            created_at TIMESTAMP DEFAULT now(),
            updated_at TIMESTAMP DEFAULT now(),
            UNIQUE(user_id, date)
        );
        
        -- Conversation summaries table for AI-generated summaries
        CREATE TABLE IF NOT EXISTS conversation_summary (
            id SERIAL PRIMARY KEY,
            conversation_id INTEGER REFERENCES conversation(id) UNIQUE,
            summary_text TEXT,
            key_topics TEXT[],
            generated_at TIMESTAMP DEFAULT now()
        );
        
        -- Add indexes for performance
        CREATE INDEX IF NOT EXISTS idx_user_engagement_date ON user_engagement_metrics(user_id, date);
        CREATE INDEX IF NOT EXISTS idx_conversation_summary_topics ON conversation_summary USING gin(key_topics);
        """
        
        return self.execute_sql(analytics_sql, "Adding analytics tables")

    def seed_initial_data(self):
        """Populate database with initial seed data"""
        logger.info("Seeding initial data")

        seed_sql = """
        -- Note: Departments will be synced from hospital database (Medecins.Specialite)
        -- No default departments inserted here - they come from hospital data

        -- Insert hospital-specific roles
        INSERT INTO role (name, description) VALUES
        ('admin', 'System Administrator - Full access to all features'),
        ('head_of_department', 'Head of Department - Departmental oversight and management'),
        ('medical_staff', 'Doctor and Nurse - Medical staff with patient data access');

        -- Note: Permissions omitted for now as RBAC is not implemented
        -- All users will have basic read access through the chatbot

        -- Insert hospital-focused topics for data retrieval
        INSERT INTO topic (name, description) VALUES
        ('Patient Information', 'Patient demographics, medical records, and personal details'),
        ('Medical Consultations', 'Consultation records, diagnoses, and treatment history'),
        ('Doctor Information', 'Doctor profiles, specialties, and contact information'),
        ('Department Statistics', 'Departmental metrics, patient distribution, and workload'),
        ('Medical History', 'Patient medical history, antecedents, and chronic conditions'),
        ('Symptoms and Observations', 'Patient symptoms, clinical observations, and vital signs'),
        ('Biometric Data', 'Patient biometric measurements, vital signs, and health indicators'),
        ('Appointment Scheduling', 'Consultation schedules, appointment history, and availability'),
        ('Hospital Analytics', 'Hospital-wide statistics, trends, and performance metrics'),
        ('Treatment Outcomes', 'Treatment effectiveness, patient outcomes, and follow-ups'),
        ('Emergency Cases', 'Emergency consultations, urgent care, and critical patients'),
        ('Medication History', 'Prescription history, medication tracking, and drug interactions'),
        ('Diagnostic Results', 'Lab results, imaging studies, and diagnostic reports'),
        ('Patient Demographics', 'Age groups, gender distribution, and population statistics'),
        ('Specialty Care', 'Specialized treatments, referrals, and expert consultations'),
        ('General Inquiries', 'General questions and system navigation help');
        """

        return self.execute_sql(seed_sql, "Seeding initial data")

    def create_migration_tracking(self):
        """Create migration tracking table and record this migration"""
        logger.info("Setting up migration tracking")

        migration_sql = """
        -- Create migrations table if it doesn't exist
        CREATE TABLE IF NOT EXISTS migrations (
            id SERIAL PRIMARY KEY,
            migration_name VARCHAR(255) UNIQUE NOT NULL,
            applied_at TIMESTAMP DEFAULT now(),
            status VARCHAR(50) DEFAULT 'applied'
        );

        -- Record this migration
        INSERT INTO migrations (migration_name, status)
        VALUES ('database_purge_and_linear_chat_setup', 'applied')
        ON CONFLICT (migration_name) DO UPDATE SET
            applied_at = now(),
            status = 'applied';
        """

        return self.execute_sql(migration_sql, "Setting up migration tracking")

    def verify_schema(self):
        """Verify that the schema was created correctly"""
        logger.info("Verifying schema integrity")

        verification_sql = """
        SELECT
            COUNT(*) as table_count,
            (SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'public') as total_tables
        FROM information_schema.tables
        WHERE table_schema = 'public'
        AND table_name IN (
            'department', 'role', 'permission', 'role_permission', 'user',
            'conversation', 'topic', 'chat_history', 'feedback', 'audit_log',
            'user_engagement_metrics', 'conversation_summary', 'migrations'
        );
        """

        try:
            cursor = self.connection.cursor()
            cursor.execute(verification_sql)
            result = cursor.fetchone()
            cursor.close()

            expected_tables = 13  # Total expected tables
            actual_tables = result[0] if result else 0

            if actual_tables == expected_tables:
                logger.info(f"✅ Schema verification passed: {actual_tables}/{expected_tables} tables created")

                # Additional verification for hospital-specific setup
                role_check_sql = "SELECT COUNT(*) FROM role WHERE name IN ('admin', 'head_of_department', 'medical_staff')"
                cursor = self.connection.cursor()
                cursor.execute(role_check_sql)
                role_count = cursor.fetchone()[0]
                cursor.close()

                topic_check_sql = "SELECT COUNT(*) FROM topic"
                cursor = self.connection.cursor()
                cursor.execute(topic_check_sql)
                topic_count = cursor.fetchone()[0]
                cursor.close()

                logger.info(f"✅ Hospital roles created: {role_count}/3")
                logger.info(f"✅ Medical topics created: {topic_count}")

                return True
            else:
                logger.error(f"❌ Schema verification failed: {actual_tables}/{expected_tables} tables created")
                return False

        except Exception as e:
            logger.error(f"Schema verification failed: {e}")
            return False

    def run_migration(self):
        """Run the complete migration process"""
        logger.info("🚀 Starting database purge and migration process")

        if not self.connect():
            return False

        try:
            # Step 1: Purge existing database
            if not self.purge_database():
                logger.error("❌ Database purge failed")
                return False

            # Step 2: Create base schema
            if not self.create_base_schema():
                logger.error("❌ Base schema creation failed")
                return False

            # Step 3: Add linear chat enhancements
            if not self.add_linear_chat_enhancements():
                logger.error("❌ Linear chat enhancements failed")
                return False

            # Step 4: Add analytics tables
            if not self.add_analytics_tables():
                logger.error("❌ Analytics tables creation failed")
                return False

            # Step 5: Seed initial data
            if not self.seed_initial_data():
                logger.error("❌ Initial data seeding failed")
                return False

            # Step 6: Create migration tracking
            if not self.create_migration_tracking():
                logger.error("❌ Migration tracking setup failed")
                return False

            # Step 7: Verify schema
            if not self.verify_schema():
                logger.error("❌ Schema verification failed")
                return False

            logger.info("🎉 Database migration completed successfully!")
            logger.info("🏥 Hospital-specific features added:")
            logger.info("   • Linear chat architecture with conversation flow constraints")
            logger.info("   • Enhanced conversation management with titles and metadata")
            logger.info("   • Hospital roles: Admin, Head of Department, Medical Staff (Doctor/Nurse)")
            logger.info("   • Medical-focused topics for patient data retrieval")
            logger.info("   • User engagement metrics for analytics dashboard")
            logger.info("   • Conversation summaries for AI-generated insights")
            logger.info("   • Comprehensive indexing for performance optimization")
            logger.info("   • Ready for department sync from hospital database (Medecins.Specialite)")

            return True

        finally:
            self.disconnect()

def main():
    """Main execution function"""
    print("🚨 DATABASE PURGE AND MIGRATION SCRIPT 🚨")
    print("=" * 50)
    print("This script will COMPLETELY PURGE the app database and recreate it.")
    print("ALL EXISTING DATA WILL BE LOST!")
    print("=" * 50)

    # Safety confirmation
    confirmation = input("Type 'PURGE_DATABASE' to confirm you want to proceed: ")
    if confirmation != 'PURGE_DATABASE':
        print("❌ Migration cancelled by user")
        return False

    migrator = DatabaseMigrator()
    success = migrator.run_migration()

    if success:
        print("\n✅ Migration completed successfully!")
        print("You can now restart your application with the new database schema.")
    else:
        print("\n❌ Migration failed! Check the logs for details.")

    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
