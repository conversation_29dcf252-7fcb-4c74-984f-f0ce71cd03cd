"""
Qdrant Setup Script for Hospital Chatbot

This script helps set up Qdrant vector database for the hospital chatbot project.
"""

import subprocess
import sys
import time
import requests
import logging
from pathlib import Path

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class QdrantSetup:
    """Helper class for Qdrant setup"""
    
    def __init__(self, host="localhost", port=6333):
        self.host = host
        self.port = port
        self.url = f"http://{host}:{port}"
    
    def check_docker_installed(self) -> bool:
        """Check if Docker is installed"""
        try:
            result = subprocess.run(
                ["docker", "--version"], 
                capture_output=True, 
                text=True, 
                check=True
            )
            logger.info(f"Docker found: {result.stdout.strip()}")
            return True
        except (subprocess.CalledProcessError, FileNotFoundError):
            logger.error("Docker not found. Please install Docker first.")
            return False
    
    def start_qdrant_docker(self) -> bool:
        """Start Qdrant using Docker"""
        try:
            logger.info("Starting Qdrant with Docker...")
            
            # Check if container already exists
            try:
                result = subprocess.run(
                    ["docker", "ps", "-a", "--filter", "name=qdrant", "--format", "{{.Names}}"],
                    capture_output=True,
                    text=True,
                    check=True
                )
                if "qdrant" in result.stdout:
                    logger.info("Qdrant container already exists. Starting it...")
                    subprocess.run(["docker", "start", "qdrant"], check=True)
                else:
                    logger.info("Creating new Qdrant container...")
                    subprocess.run([
                        "docker", "run", "-d",
                        "--name", "qdrant",
                        "-p", f"{self.port}:6333",
                        "-v", "qdrant_data:/qdrant/storage",
                        "qdrant/qdrant"
                    ], check=True)
                
                logger.info("Qdrant container started successfully!")
                return True
                
            except subprocess.CalledProcessError as e:
                logger.error(f"Error managing Docker container: {e}")
                return False
                
        except Exception as e:
            logger.error(f"Error starting Qdrant: {e}")
            return False
    
    def wait_for_qdrant(self, timeout=30) -> bool:
        """Wait for Qdrant to be ready"""
        logger.info(f"Waiting for Qdrant to be ready at {self.url}...")
        
        start_time = time.time()
        while time.time() - start_time < timeout:
            try:
                response = requests.get(f"{self.url}/health", timeout=5)
                if response.status_code == 200:
                    logger.info("Qdrant is ready!")
                    return True
            except requests.RequestException:
                pass
            
            time.sleep(2)
        
        logger.error(f"Qdrant not ready after {timeout} seconds")
        return False
    
    def check_qdrant_status(self) -> bool:
        """Check if Qdrant is running"""
        try:
            response = requests.get(f"{self.url}/health", timeout=5)
            if response.status_code == 200:
                logger.info("Qdrant is running and healthy!")
                return True
        except requests.RequestException:
            logger.info("Qdrant is not accessible")
        return False
    
    def install_python_dependencies(self) -> bool:
        """Install required Python packages"""
        try:
            logger.info("Installing Python dependencies...")
            subprocess.run([
                sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
            ], check=True)
            logger.info("Python dependencies installed successfully!")
            return True
        except subprocess.CalledProcessError as e:
            logger.error(f"Error installing dependencies: {e}")
            return False
    
    def create_directories(self):
        """Create necessary directories"""
        dirs = ["processed_data", "logs"]
        for dir_name in dirs:
            Path(dir_name).mkdir(exist_ok=True)
        logger.info("Created necessary directories")

def main():
    """Main setup function"""
    logger.info("=== Hospital Chatbot Qdrant Setup ===")
    
    setup = QdrantSetup()
    
    # Step 1: Check Docker
    if not setup.check_docker_installed():
        logger.error("Please install Docker and try again.")
        sys.exit(1)
    
    # Step 2: Create directories
    setup.create_directories()
    
    # Step 3: Install Python dependencies
    if not setup.install_python_dependencies():
        logger.error("Failed to install Python dependencies")
        sys.exit(1)
    
    # Step 4: Start Qdrant
    if not setup.start_qdrant_docker():
        logger.error("Failed to start Qdrant")
        sys.exit(1)
    
    # Step 5: Wait for Qdrant to be ready
    if not setup.wait_for_qdrant():
        logger.error("Qdrant failed to start properly")
        sys.exit(1)
    
    # Step 6: Final check
    if setup.check_qdrant_status():
        logger.info("✅ Setup completed successfully!")
        logger.info("You can now run: python process_hospital_data.py")
        logger.info(f"Qdrant Web UI available at: http://localhost:{setup.port}/dashboard")
    else:
        logger.error("❌ Setup completed with errors")
        sys.exit(1)

if __name__ == "__main__":
    main()
