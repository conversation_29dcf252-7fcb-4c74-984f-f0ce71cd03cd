"""
Advanced Medical Record Search with Filters

This script provides advanced search capabilities with filters for
medical specialty, date ranges, patient demographics, and more.
"""

import os
import sys
import json
import argparse
from pathlib import Path
from typing import List, Dict, Optional, Tuple
from datetime import datetime
import re

# Add the current directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from medical_record_embedder import MedicalRecordEmbedder
from config import *

class AdvancedMedicalSearch:
    """Advanced search with filters and analytics"""
    
    def __init__(self):
        self.embedder = MedicalRecordEmbedder(model_name=EMBEDDING_MODEL)
        self.initialized = False
        
    def initialize(self):
        """Initialize the search system"""
        if self.initialized:
            return True
            
        try:
            self.embedder.setup_qdrant(host=QDRANT_HOST, port=QDRANT_PORT)
            self.initialized = True
            return True
        except Exception as e:
            print(f"❌ Error initializing: {e}")
            return False
    
    def search_with_filters(self, 
                          query: str,
                          specialty: Optional[str] = None,
                          limit: int = 10,
                          min_score: float = 0.3,
                          patient_age_range: Optional[Tuple[int, int]] = None,
                          has_medication: Optional[str] = None,
                          exclude_specialty: Optional[str] = None) -> List[Dict]:
        """
        Advanced search with multiple filters
        
        Args:
            query: Search query
            specialty: Filter by medical specialty
            limit: Maximum results
            min_score: Minimum similarity score
            patient_age_range: Tuple of (min_age, max_age)
            has_medication: Filter by medication name
            exclude_specialty: Exclude specific specialty
            
        Returns:
            Filtered search results
        """
        if not self.initialize():
            return []
        
        try:
            # Get initial results
            results = self.embedder.query_similar_records(query, limit=limit*2)  # Get more to filter
            
            filtered_results = []
            
            for result in results:
                record = result['record']
                
                # Score filter
                if result['score'] < min_score:
                    continue
                
                # Specialty filter
                if specialty and record['record_type'].lower() != specialty.lower():
                    continue
                
                # Exclude specialty filter
                if exclude_specialty and record['record_type'].lower() == exclude_specialty.lower():
                    continue
                
                # Age filter (if age can be extracted)
                if patient_age_range:
                    age = self._extract_age(record)
                    if age and (age < patient_age_range[0] or age > patient_age_range[1]):
                        continue
                
                # Medication filter
                if has_medication:
                    if not any(has_medication.lower() in med.lower() for med in record['medications']):
                        continue
                
                filtered_results.append(result)
                
                if len(filtered_results) >= limit:
                    break
            
            return filtered_results
            
        except Exception as e:
            print(f"❌ Search error: {e}")
            return []
    
    def _extract_age(self, record: Dict) -> Optional[int]:
        """Extract patient age from record"""
        try:
            # Look for age in patient_id or specialized_content
            text = f"{record['patient_id']} {record['specialized_content']}"
            
            # Pattern for "Age: XX" or "Âge: XX"
            age_match = re.search(r'[Aa]ge?\s*[:\-]\s*(\d+)', text)
            if age_match:
                return int(age_match.group(1))
            
            # Pattern for "XX ans" (French for "XX years old")
            age_match = re.search(r'(\d+)\s*ans', text)
            if age_match:
                return int(age_match.group(1))
            
            return None
        except:
            return None
    
    def search_by_diagnosis(self, diagnosis_keywords: List[str], limit: int = 10) -> List[Dict]:
        """
        Search specifically by diagnosis keywords
        
        Args:
            diagnosis_keywords: List of diagnosis-related keywords
            limit: Maximum results
            
        Returns:
            Search results focused on diagnosis
        """
        query = ' '.join(diagnosis_keywords)
        results = self.search_with_filters(query, limit=limit, min_score=0.4)
        
        # Re-rank by diagnosis relevance
        for result in results:
            diagnosis_text = result['record']['primary_diagnosis'].lower()
            relevance_boost = 0
            
            for keyword in diagnosis_keywords:
                if keyword.lower() in diagnosis_text:
                    relevance_boost += 0.1
            
            result['diagnosis_relevance'] = relevance_boost
        
        # Sort by combined score and diagnosis relevance
        results.sort(key=lambda x: x['score'] + x.get('diagnosis_relevance', 0), reverse=True)
        
        return results
    
    def search_by_symptoms(self, symptoms: List[str], limit: int = 10) -> List[Dict]:
        """
        Search by symptoms and clinical presentation
        
        Args:
            symptoms: List of symptoms
            limit: Maximum results
            
        Returns:
            Search results focused on symptoms
        """
        # Create symptom-focused query
        symptom_query = f"patient with {' and '.join(symptoms)}"
        
        results = self.search_with_filters(symptom_query, limit=limit, min_score=0.3)
        
        # Boost results that contain multiple symptoms
        for result in results:
            content = result['record']['specialized_content'].lower()
            symptom_matches = sum(1 for symptom in symptoms if symptom.lower() in content)
            result['symptom_matches'] = symptom_matches
        
        # Sort by symptom matches and score
        results.sort(key=lambda x: (x['symptom_matches'], x['score']), reverse=True)
        
        return results
    
    def get_similar_cases(self, reference_file: str, limit: int = 5) -> List[Dict]:
        """
        Find cases similar to a reference medical record
        
        Args:
            reference_file: Path to reference medical record
            limit: Maximum results
            
        Returns:
            Similar cases
        """
        try:
            # Parse reference record
            reference_record = self.embedder.parse_medical_record(reference_file)
            if not reference_record:
                print(f"❌ Could not parse reference file: {reference_file}")
                return []
            
            # Create query from reference record
            query_parts = []
            if reference_record.primary_diagnosis:
                query_parts.append(reference_record.primary_diagnosis)
            if reference_record.medications:
                query_parts.extend(reference_record.medications[:2])
            
            query = ' '.join(query_parts)
            
            # Search for similar cases
            results = self.search_with_filters(
                query, 
                specialty=reference_record.record_type,
                limit=limit,
                min_score=0.4
            )
            
            # Remove the reference file from results
            results = [r for r in results if r['record']['file_path'] != reference_file]
            
            return results
            
        except Exception as e:
            print(f"❌ Error finding similar cases: {e}")
            return []
    
    def analyze_search_patterns(self, queries: List[str]) -> Dict:
        """
        Analyze search patterns and provide insights
        
        Args:
            queries: List of search queries to analyze
            
        Returns:
            Analysis results
        """
        analysis = {
            'total_queries': len(queries),
            'specialty_distribution': {},
            'common_diagnoses': {},
            'average_scores': [],
            'query_performance': {}
        }
        
        for query in queries:
            results = self.search_with_filters(query, limit=5)
            
            if results:
                avg_score = sum(r['score'] for r in results) / len(results)
                analysis['average_scores'].append(avg_score)
                analysis['query_performance'][query] = {
                    'results_count': len(results),
                    'avg_score': avg_score,
                    'top_score': results[0]['score']
                }
                
                # Analyze specialties
                for result in results:
                    specialty = result['record']['record_type']
                    analysis['specialty_distribution'][specialty] = \
                        analysis['specialty_distribution'].get(specialty, 0) + 1
                    
                    # Analyze diagnoses
                    diagnosis = result['record']['primary_diagnosis']
                    analysis['common_diagnoses'][diagnosis] = \
                        analysis['common_diagnoses'].get(diagnosis, 0) + 1
        
        return analysis
    
    def export_search_results(self, results: List[Dict], filename: str = "search_export.json"):
        """
        Export search results to JSON file
        
        Args:
            results: Search results to export
            filename: Output filename
        """
        export_data = {
            'export_date': datetime.now().isoformat(),
            'total_results': len(results),
            'results': []
        }
        
        for result in results:
            export_data['results'].append({
                'score': result['score'],
                'patient_id': result['record']['patient_id'],
                'record_type': result['record']['record_type'],
                'primary_diagnosis': result['record']['primary_diagnosis'],
                'medications': result['record']['medications'],
                'allergies': result['record']['allergies'],
                'physician': result['record']['physician'],
                'department': result['record']['department'],
                'file_path': result['record']['file_path'],
                'specialized_content': result['record']['specialized_content'][:500]  # Truncate for export
            })
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, ensure_ascii=False, indent=2)
        
        print(f"✅ Results exported to {filename}")

def create_argument_parser():
    """Create command line argument parser"""
    parser = argparse.ArgumentParser(description='Advanced Medical Record Search')
    
    parser.add_argument('query', nargs='*', help='Search query')
    parser.add_argument('--specialty', '-s', help='Filter by medical specialty')
    parser.add_argument('--limit', '-l', type=int, default=10, help='Maximum results')
    parser.add_argument('--min-score', '-m', type=float, default=0.3, help='Minimum similarity score')
    parser.add_argument('--age-min', type=int, help='Minimum patient age')
    parser.add_argument('--age-max', type=int, help='Maximum patient age')
    parser.add_argument('--medication', help='Filter by medication name')
    parser.add_argument('--exclude-specialty', help='Exclude specific specialty')
    parser.add_argument('--export', help='Export results to JSON file')
    parser.add_argument('--similar-to', help='Find cases similar to reference file')
    parser.add_argument('--diagnoses', nargs='+', help='Search by specific diagnoses')
    parser.add_argument('--symptoms', nargs='+', help='Search by symptoms')
    parser.add_argument('--analyze', action='store_true', help='Analyze search patterns')
    
    return parser

def main():
    """Main function"""
    parser = create_argument_parser()
    args = parser.parse_args()
    
    search = AdvancedMedicalSearch()
    
    # Handle different search modes
    if args.similar_to:
        # Find similar cases
        results = search.get_similar_cases(args.similar_to, limit=args.limit)
        query_desc = f"Similar to {args.similar_to}"
    elif args.diagnoses:
        # Search by diagnoses
        results = search.search_by_diagnosis(args.diagnoses, limit=args.limit)
        query_desc = f"Diagnoses: {', '.join(args.diagnoses)}"
    elif args.symptoms:
        # Search by symptoms
        results = search.search_by_symptoms(args.symptoms, limit=args.limit)
        query_desc = f"Symptoms: {', '.join(args.symptoms)}"
    elif args.analyze:
        # Analyze search patterns
        test_queries = [
            "heart attack chest pain",
            "diabetes high glucose",
            "teenage depression suicide",
            "abdominal pain surgery",
            "elderly breathing problems"
        ]
        analysis = search.analyze_search_patterns(test_queries)
        print("📊 Search Pattern Analysis:")
        print(json.dumps(analysis, indent=2))
        return
    elif args.query:
        # Regular search with filters
        query = ' '.join(args.query)
        age_range = None
        if args.age_min is not None or args.age_max is not None:
            age_range = (args.age_min or 0, args.age_max or 150)
        
        results = search.search_with_filters(
            query=query,
            specialty=args.specialty,
            limit=args.limit,
            min_score=args.min_score,
            patient_age_range=age_range,
            has_medication=args.medication,
            exclude_specialty=args.exclude_specialty
        )
        query_desc = query
    else:
        # Show help if no arguments
        parser.print_help()
        return
    
    # Display results
    print(f"\n🔍 Search: {query_desc}")
    print(f"📋 Found {len(results)} results")
    print("=" * 60)
    
    for i, result in enumerate(results, 1):
        record = result['record']
        print(f"\n{i}. Score: {result['score']:.3f}")
        print(f"   File: {Path(record['file_path']).name}")
        print(f"   Patient: {record['patient_id']}")
        print(f"   Type: {record['record_type'].capitalize()}")
        print(f"   Diagnosis: {record['primary_diagnosis']}")
        
        if record['medications']:
            print(f"   Medications: {', '.join(record['medications'][:3])}")
        
        if record['allergies']:
            print(f"   Allergies: {', '.join(record['allergies'])}")
        
        print(f"   Department: {record['department']}")
        print("-" * 40)
    
    # Export if requested
    if args.export:
        search.export_search_results(results, args.export)

if __name__ == "__main__":
    main()
