import os
import re
import json
import time
import traceback
from decimal import Decimal
from typing import Union, Tuple, List, Dict, Any
import pandas as pd
import plotly.graph_objects as go
import plotly.express as px
from vanna.ollama import Ollama
from vanna.chromadb import ChromaDB_VectorStore
# Flask import removed for FastAPI compatibility

class MyVanna(ChromaDB_VectorStore, Ollama):
    """Combined ChromaDB vector store and Ollama LLM, with custom behavior"""
    
    def __init__(self, config=None):
        ChromaDB_VectorStore.__init__(self, config=config)

        # Initialize Ollama with proper client configuration
        self.config = config
        self.model = config.get('model') if config else None

        # Initialize the Ollama client with proper HTTPS support
        self._initialize_ollama_client()

        # Call parent Ollama init but skip client initialization
        # since we've already done it with proper configuration
        try:
            # Temporarily disable model pulling during init
            original_pull = getattr(self, '_MyVanna__pull_model_if_ne', None)
            if hasattr(self, '_MyVanna__pull_model_if_ne'):
                delattr(self, '_MyVanna__pull_model_if_ne')

            # Initialize Ollama base class
            from vanna.base import VannaBase
            VannaBase.__init__(self, config=config)

            # Restore model pulling method if it existed
            if original_pull:
                setattr(self, '_MyVanna__pull_model_if_ne', original_pull)

        except Exception as e:
            print(f"Warning during Ollama initialization: {e}")
            # Fallback to basic initialization
            from vanna.base import VannaBase
            VannaBase.__init__(self, config=config)
        
        # Ensure base VannaBase attributes are initialized
        if not hasattr(self, 'language'): 
            self.language = config.get("language", None) if config else None
        if not hasattr(self, 'dialect'): 
            self.dialect = config.get("dialect", "SQL") if config else "SQL"
        if not hasattr(self, 'max_tokens'): 
            self.max_tokens = config.get("max_tokens", 14000) if config else 14000
        if not hasattr(self, 'static_documentation'): 
            self.static_documentation = ""
        if not hasattr(self, 'config'): 
            self.config = config if config is not None else {}
        
        # Initialize chat history
        self.chat_history = []
        self.client = None

    def _initialize_ollama_client(self):
        """Initialize Ollama client with proper HTTPS configuration"""
        from ollama import Client

        if not self.config:
            self.client = Client()
            return

        client_config = {}
        host = self.config.get('host')
        port = self.config.get('port', 11434)

        if host:
            # Check if host already includes protocol
            if host.startswith(('http://', 'https://')):
                client_config['host'] = host
            else:
                # For ngrok tunnels, use HTTPS if port is 443
                protocol = 'https' if port == 443 or 'ngrok' in host else 'http'
                client_config['host'] = f"{protocol}://{host}:{port}"

        try:
            self.client = Client(**client_config)
            self.ollama_client = self.client  # For Vanna compatibility
            print(f"✅ Ollama client initialized with host: {client_config.get('host', 'default')}")
        except Exception as e:
            print(f"❌ Failed to initialize Ollama client: {e}")
            # Fallback to default client
            self.client = Client()
            self.ollama_client = self.client

    def log(self, message: str, title: str = None):
        """Consolidate logging to standard logger"""
        import logging
        logger = logging.getLogger(__name__)
        log_message = f"Vanna: {title} - {message}" if title else f"Vanna: {message}"
        logger.info(log_message)

    def _serialize_dataframe_for_json(self, df: pd.DataFrame, max_rows: int = None) -> List[Dict]:
        """Convert DataFrame to JSON-serializable format, handling Decimal and other types"""
        try:
            # Import config to check if we should use full dataframe
            try:
                from backend.config import Config
                config = Config()
            except ImportError:
                from config import Config
                config = Config()

            # Determine how many rows to include
            if max_rows is None:
                if config.USE_FULL_DATAFRAME_CONTEXT:
                    max_rows = min(len(df), config.MAX_CONTEXT_ROWS)
                else:
                    max_rows = 5  # Default behavior

            # Get data (full or preview)
            preview_df = df.head(max_rows) if len(df) > max_rows else df
            results_dict = preview_df.to_dict('records')

            # Convert non-serializable types
            for row in results_dict:
                for key, value in row.items():
                    if isinstance(value, Decimal):
                        row[key] = float(value)
                    elif pd.isna(value):
                        row[key] = None
                    elif hasattr(value, '__class__') and 'Decimal' in str(value.__class__):
                        row[key] = float(value)
                    elif isinstance(value, (pd.Timestamp, pd.Timedelta)):
                        row[key] = str(value)

            return results_dict
        except Exception as e:
            self.log(title="DataFrame Serialization Error", message=f"Error serializing DataFrame: {str(e)}")
            return []

    def extract_sql(self, llm_output: str) -> str:
        """Remove <think>...</think> blocks before SQL extraction"""
        cleaned_output = re.sub(r"<think>.*?</think>", "", llm_output, flags=re.DOTALL)
        return super().extract_sql(cleaned_output)

    def _sanitize_plotly_code(self, raw_plotly_code: str) -> str:
        """
        Enhanced sanitization of Plotly code to fix common issues.
        Overrides the base class method to handle uniformtext and other property issues.
        """
        # Start with the base sanitization (removes fig.show())
        plotly_code = super()._sanitize_plotly_code(raw_plotly_code)

        # Fix uniformtext property issues
        # Replace uniformtext=False with proper removal or correct structure
        plotly_code = re.sub(
            r'uniformtext\s*=\s*False\b',
            '',
            plotly_code,
            flags=re.IGNORECASE
        )

        # Replace uniformtext=True with proper dictionary structure
        plotly_code = re.sub(
            r'uniformtext\s*=\s*True\b',
            'uniformtext={"mode": "hide", "minsize": 8}',
            plotly_code,
            flags=re.IGNORECASE
        )

        # Clean up any trailing commas that might result from removing properties
        plotly_code = re.sub(r',\s*,', ',', plotly_code)  # Remove double commas
        plotly_code = re.sub(r',\s*\)', ')', plotly_code)  # Remove trailing comma before closing paren
        plotly_code = re.sub(r',\s*}', '}', plotly_code)  # Remove trailing comma before closing brace

        # Clean up any empty parameter lists in update_layout calls
        plotly_code = re.sub(r'\.update_layout\(\s*,', '.update_layout(', plotly_code)
        plotly_code = re.sub(r'\.update_layout\(\s*\)', '.update_layout()', plotly_code)

        return plotly_code

    def submit_prompt(self, prompt_messages: list, stream: bool = False, **kwargs) -> Union[str, iter]:
        """Submit a prompt to the Ollama model with streaming support"""
        if not hasattr(self, 'client') or self.client is None:
            from ollama import Client
            client_config = {}
            if self.config and 'host' in self.config:
                # For ngrok or external hosts, construct the full URL
                host = self.config['host']
                port = self.config.get('port', 11434)

                # Check if host already includes protocol
                if host.startswith(('http://', 'https://')):
                    client_config['host'] = host
                else:
                    # For ngrok tunnels, use HTTP unless port is 443 (HTTPS)
                    protocol = 'https' if port == 443 else 'http'
                    client_config['host'] = f"{protocol}://{host}:{port}"
            try:
                self.client = Client(**client_config)
                print(f"✅ Ollama client initialized with host: {client_config.get('host', 'default')}")
            except Exception as e:
                raise ConnectionError(f"Ollama client not initialized and failed to auto-initialize: {e}")

        model_name = self.config.get('model', os.environ.get('OLLAMA_MODEL', 'phi4-mini:latest'))
        
        # Log what's being fed to the model
        print("\n" + "="*80)
        print("🤖 MODEL INPUT LOGGING")
        print("="*80)
        print(f"📅 Timestamp: {time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"🎯 Model: {model_name}")
        print(f"🌊 Streaming: {stream}")
        print(f"⚙️  Options: {kwargs.get('options', 'None')}")
        print(f"📝 Number of messages: {len(prompt_messages)}")
        print("\n📋 FULL PROMPT MESSAGES:")
        print("-" * 40)
        
        for i, message in enumerate(prompt_messages):
            role = message.get('role', 'unknown')
            content = message.get('content', '')
            
            print(f"\n[Message {i+1}] Role: {role.upper()}")
            print(f"Content length: {len(content)} characters")
            print("Content preview:")
            print("-" * 20)
            
            # Show content with proper formatting
            if len(content) > 1000:
                print(content[:500])
                print("\n... [TRUNCATED - showing first 500 chars] ...")
                print(f"\n... [FULL CONTENT LENGTH: {len(content)} chars] ...")
                print("... [LAST 500 CHARS] ...")
                print(content[-500:])
            else:
                print(content)
            print("-" * 20)
        
        print("\n" + "="*80)
        print("END OF MODEL INPUT")
        print("="*80 + "\n")

        if stream:
            response_stream = self.client.chat(
                model=model_name,
                messages=prompt_messages,
                stream=True,
                options=kwargs.get('options') 
            )
            
            def content_stream():
                buffer = ""
                state = "NORMAL"
                full_response_buffer = ""  # Track full response for logging
                
                print("\n" + "🔄" * 20)
                print("📡 STREAMING MODEL RESPONSE START")
                print("🔄" * 20)
                
                for chunk_data in response_stream:
                    if chunk_data.get('done') and not chunk_data.get('message', {}).get('content'):
                        if state == "NORMAL" and buffer:
                            yield json.dumps({"type": "content_chunk", "content": buffer})
                            buffer = ""
                        break
                    
                    if chunk_data.get('message') and chunk_data['message'].get('content'):
                        chunk_content = chunk_data['message']['content']
                        buffer += chunk_content
                        full_response_buffer += chunk_content
                        print(chunk_content, end='', flush=True)  # Real-time output

                    while True:
                        if state == "NORMAL":
                            think_start_index = buffer.find("<think>")
                            if think_start_index != -1:
                                content_before_think = buffer[:think_start_index]
                                if content_before_think:
                                    yield json.dumps({"type": "content_chunk", "content": content_before_think})
                                yield json.dumps({"type": "think_start_marker"})
                                buffer = buffer[think_start_index + len("<think>"):]
                                state = "INSIDE_THINK_BLOCK"
                            else:
                                if buffer:
                                    yield json.dumps({"type": "content_chunk", "content": buffer})
                                buffer = ""
                                break
                        
                        elif state == "INSIDE_THINK_BLOCK":
                            think_end_index = buffer.find("</think>")
                            if think_end_index != -1:
                                yield json.dumps({"type": "think_end_marker"})
                                buffer = buffer[think_end_index + len("</think>"):]
                                state = "NORMAL"
                            else:
                                break
                
                if state == "NORMAL" and buffer:
                    yield json.dumps({"type": "content_chunk", "content": buffer})
                
                print("\n" + "🔄" * 20)
                print("📡 STREAMING MODEL RESPONSE END")
                print(f"📏 Total response length: {len(full_response_buffer)} characters")
                print("🔄" * 20 + "\n")

            return content_stream()
        else:
            response = self.client.chat(
                model=model_name,
                messages=prompt_messages,
                stream=False,
                options=kwargs.get('options')
            )
            content_with_thoughts = response.get('message', {}).get('content', '')
            
            # Log non-streaming response
            print("\n" + "📤" * 20)
            print("📡 NON-STREAMING MODEL RESPONSE")
            print("📤" * 20)
            print(f"📏 Raw response length: {len(content_with_thoughts)} characters")
            print("📝 Raw response content:")
            print("-" * 40)
            print(content_with_thoughts)
            print("-" * 40)
            
            # Clean the content to remove <think> blocks
            cleaned_content = re.sub(r"<think>.*?</think>", "", content_with_thoughts, flags=re.DOTALL).strip()
            
            print(f"✨ Cleaned response length: {len(cleaned_content)} characters")
            print("🧹 Cleaned response content:")
            print("-" * 40)
            print(cleaned_content)
            print("-" * 40)
            print("📤" * 20 + "\n")
            
            return cleaned_content

    def generate_interactive_response(self, question: str, current_id: str, allow_llm_to_see_data: bool = True, **kwargs):
        """Generates an interactive response, streaming chat chunks and identifying SQL with intermediate SQL support"""
        import logging
        logger = logging.getLogger(__name__)
        logger.info(f"Starting interactive response generation with chat history: {len(self.chat_history)} messages")
        
        initial_prompt_config = self.config.get("initial_prompt", None)

        # Optimize context retrieval - limit to most relevant items
        question_sql_list = self.get_similar_question_sql(question, **kwargs)
        if question_sql_list:
            question_sql_list = question_sql_list[:3]  # Limit to top 3 most relevant examples

        ddl_list = self.get_related_ddl(question, **kwargs)
        if ddl_list:
            ddl_list = ddl_list[:5]  # Limit to top 5 most relevant tables

        doc_list = self.get_related_documentation(question, **kwargs)
        if doc_list:
            doc_list = doc_list[:2]  # Limit to top 2 most relevant docs
        
        # Log context data being fed to the model
        print("\n" + "🎯" * 20)
        print("📋 CONTEXT DATA FOR MODEL")
        print("🎯" * 20)
        print(f"❓ User Question: {question}")
        print(f"🔧 Initial Prompt Config: {initial_prompt_config is not None}")
        print(f"📊 Similar Question-SQL pairs: {len(question_sql_list) if question_sql_list else 0}")
        print(f"🏗️  DDL schemas: {len(ddl_list) if ddl_list else 0}")
        print(f"📚 Documentation: {len(doc_list) if doc_list else 0}")
        print(f"💬 Chat History: {len(self.chat_history) if self.chat_history else 0} messages")
        
        if question_sql_list:
            print("\n📊 SIMILAR QUESTION-SQL PAIRS:")
            for i, pair in enumerate(question_sql_list[:3]):  # Show first 3
                print(f"  [{i+1}] Question: {pair.get('question', 'N/A')}")
                print(f"      SQL: {pair.get('sql', 'N/A')}")
        
        if ddl_list:
            print("\n🏗️ DDL SCHEMAS:")
            for i, ddl in enumerate(ddl_list[:3]):  # Show first 3
                print(f"  [{i+1}] {ddl[:100]}..." if len(ddl) > 100 else f"  [{i+1}] {ddl}")
        
        if doc_list:
            print("\n📚 DOCUMENTATION:")
            for i, doc in enumerate(doc_list[:3]):  # Show first 3
                print(f"  [{i+1}] {doc[:100]}..." if len(doc) > 100 else f"  [{i+1}] {doc}")
        
        if self.chat_history:
            print("\n💬 CHAT HISTORY:")
            for i, msg in enumerate(self.chat_history[-12:]):  # Show last 6 messages to include summaries
                role = msg.get('role', 'unknown')
                content = msg.get('content', '')
                # Highlight summary and SQL result messages
                if content.startswith('[SUMMARY]'):
                    print(f"  [{i+1}] {role.upper()} (SUMMARY): {content[:100]}..." if len(content) > 100 else f"  [{i+1}] {role.upper()} (SUMMARY): {content}")
                elif content.startswith('[SQL_RESULT]'):
                    print(f"  [{i+1}] {role.upper()} (SQL_RESULT): {content[:100]}..." if len(content) > 100 else f"  [{i+1}] {role.upper()} (SQL_RESULT): {content}")
                else:
                    print(f"  [{i+1}] {role.upper()}: {content[:80]}..." if len(content) > 80 else f"  [{i+1}] {role.upper()}: {content}")
        
        print("🎯" * 20 + "\n")
        
        prompt = self.get_sql_prompt(
            initial_prompt_text=initial_prompt_config,
            question=question,
            question_sql_list=question_sql_list,
            ddl_list=ddl_list,
            doc_list=doc_list,
            chat_history=self.chat_history,
            **kwargs,
        )
        
        log_message = json.dumps(prompt, indent=2) if isinstance(prompt, (list, dict)) else prompt
        self.log(title="Interactive SQL/Chat Prompt", message=log_message)

        llm_stream = self.submit_prompt(prompt, stream=True, **kwargs)
        
        buffer = []
        for stream_item_json_str in llm_stream:
            try:
                stream_item = json.loads(stream_item_json_str)
                item_type = stream_item.get("type")
                
                if item_type == "think_start_marker":
                    yield f"data: {json.dumps({'type': 'thinking_on', 'id': current_id})}\n\n"
                elif item_type == "think_end_marker":
                    yield f"data: {json.dumps({'type': 'thinking_off', 'id': current_id})}\n\n"
                elif item_type == "content_chunk":
                    content = stream_item.get("content", "")
                    if content:
                        buffer.append(content)
                        yield f"data: {json.dumps({'type': 'chunk', 'content': content, 'id': current_id})}\n\n"
                else:
                    import logging
                    logger = logging.getLogger(__name__)
                    logger.warning(f"Unexpected stream item type: {item_type}")

            except json.JSONDecodeError:
                import logging
                logger = logging.getLogger(__name__)
                logger.error(f"JSONDecodeError in stream from submit_prompt: {stream_item_json_str}")
                pass

        final_response_text = "".join(buffer)
        self.log(title="LLM Final Streamed Response", message=final_response_text)

        # Update server-side chat history
        self.chat_history.append(self.user_message(question))
        self.chat_history.append(self.assistant_message(final_response_text))

        # Cache chat history and question for potential intermediate SQL processing
        from main import get_cache
        cache = get_cache()
        if cache:
            # Ensure chat history is preserved between sessions
            cache.set(id=current_id, field='chat_history', value=self.chat_history.copy())
            cache.set(id=current_id, field='question', value=question)
            
            # Log that we saved the chat history
            logger.info(f"Saved chat history to cache for ID {current_id}: {len(self.chat_history)} messages")

        # Handle intermediate SQL processing
        if 'intermediate_sql' in final_response_text.lower():
            if not allow_llm_to_see_data:
                yield f"data: {json.dumps({'type': 'chat_result', 'id': current_id, 'message': 'The LLM is not allowed to see the data in your database. Your question requires database introspection to generate the necessary SQL. Please enable data access to continue.', 'stream_end': True})}\n\n"
                return

            # Process intermediate SQL
            yield from self._process_intermediate_sql(question, final_response_text, current_id, initial_prompt_config, question_sql_list, ddl_list, doc_list, **kwargs)
        else:
            # Regular SQL processing
            extracted_sql = self.extract_sql(final_response_text)
            if self.is_sql_valid(extracted_sql):
                cache.set(id=current_id, field='sql', value=extracted_sql)
                yield f"data: {json.dumps({'type': 'sql_result', 'id': current_id, 'text': extracted_sql, 'full_response': final_response_text, 'stream_end': True})}\n\n"
            else:
                yield f"data: {json.dumps({'type': 'chat_result', 'id': current_id, 'message': final_response_text, 'stream_end': True})}\n\n"

    def _is_intermediate_sql(self, llm_response: str) -> bool:
        """Check if the LLM response contains intermediate SQL"""
        return 'intermediate_sql' in llm_response.lower()

    def _process_intermediate_sql(self, question: str, llm_response: str, current_id: str,
                                 initial_prompt_config: str, question_sql_list: list,
                                 ddl_list: list, doc_list: list, **kwargs):
        """Process multiple intermediate SQL queries and generate final SQL with enhanced streaming"""
        try:
            # Initialize tracking for multiple intermediate queries
            intermediate_queries = []
            intermediate_results = []
            max_iterations = 5  # Prevent infinite loops
            iteration_count = 0
            current_response = llm_response

            # Enhanced user notification about intermediate SQL process
            yield f"data: {json.dumps({'type': 'intermediate_notification', 'id': current_id, 'message': 'The system has decided to run intermediate SQL queries to explore the available data, then generate the final optimized query based on those results.'})}\n\n"

            # Loop to handle multiple intermediate SQL queries
            while self._is_intermediate_sql(current_response) and iteration_count < max_iterations:
                iteration_count += 1

                # Extract and execute current intermediate SQL
                intermediate_sql = self.extract_sql(current_response)

                if not self.is_sql_valid(intermediate_sql):
                    yield f"data: {json.dumps({'type': 'error', 'id': current_id, 'error': f'Invalid intermediate SQL generated in iteration {iteration_count}', 'stream_end': True})}\n\n"
                    return

                # Show the intermediate SQL query with iteration number
                yield f"data: {json.dumps({'type': 'intermediate_sql', 'id': current_id, 'sql': intermediate_sql, 'iteration': iteration_count, 'message': f'Executing intermediate query #{iteration_count} to explore data...'})}\n\n"

                self.log(title=f"Running Intermediate SQL #{iteration_count}", message=intermediate_sql)

                # Execute intermediate SQL
                df = self.run_sql(intermediate_sql)

                # Store this intermediate query and result
                intermediate_queries.append(intermediate_sql)
                intermediate_results.append(df)

                # Add intermediate SQL results to chat history for persistent model context
                self.add_dataframe_to_chat_history(sql=intermediate_sql, df=df, cache_id=current_id, is_intermediate=True)
                self.log(title=f"Added Intermediate SQL #{iteration_count} Results to Chat History", message=f"Added {len(df)} rows of intermediate results to model context")

                # Show intermediate results to user with proper JSON serialization
                results_dict = self._serialize_dataframe_for_json(df, max_rows=5)

                if results_dict:
                    yield f"data: {json.dumps({'type': 'intermediate_results', 'id': current_id, 'results': results_dict, 'total_rows': len(df), 'iteration': iteration_count, 'message': f'Intermediate query #{iteration_count} found {len(df)} rows.'})}\n\n"
                else:
                    # Fallback: send results without data, just the count
                    yield f"data: {json.dumps({'type': 'intermediate_results', 'id': current_id, 'results': [], 'total_rows': len(df), 'iteration': iteration_count, 'message': f'Intermediate query #{iteration_count} found {len(df)} rows (data preview unavailable).'})}\n\n"

                # Check if we're about to exceed max iterations before making LLM call
                if iteration_count >= max_iterations:
                    self.log(title="Max Iterations Reached", message=f"Reached maximum iterations ({max_iterations}), stopping intermediate processing")
                    break

                # Build accumulated context from all intermediate results
                accumulated_doc_list = list(doc_list)
                for i, (query, result) in enumerate(zip(intermediate_queries, intermediate_results), 1):
                    intermediate_doc = f"The following is a pandas DataFrame with the results of intermediate SQL query #{i} ({query}): \n" + result.to_markdown()
                    accumulated_doc_list.append(intermediate_doc)

                # Generate next SQL prompt with all accumulated intermediate results
                next_prompt = self.get_final_sql_prompt(
                    initial_prompt_text=initial_prompt_config,
                    question=question,
                    intermediate_queries=intermediate_queries,
                    intermediate_results=intermediate_results,
                    question_sql_list=question_sql_list,
                    ddl_list=ddl_list,
                    doc_list=accumulated_doc_list,
                    chat_history=self.chat_history,
                    iteration_count=iteration_count,
                    **kwargs,
                )

                self.log(title=f"Next SQL Prompt (Iteration {iteration_count})", message=json.dumps(next_prompt, indent=2))

                # Notify user about next iteration (only if we're going to continue and haven't hit max)
                if iteration_count < max_iterations:
                    yield f"data: {json.dumps({'type': 'next_iteration_start', 'id': current_id, 'iteration': iteration_count + 1, 'message': f'Generating next query based on {iteration_count} intermediate result(s)...'})}\n\n"

                # Generate next SQL with streaming - start fresh for each iteration
                next_llm_stream = self.submit_prompt(next_prompt, stream=True, **kwargs)
                next_response_text = ""  # Fresh response for this iteration

                for stream_item_json_str in next_llm_stream:
                    try:
                        stream_item = json.loads(stream_item_json_str)
                        item_type = stream_item.get("type")

                        if item_type == "think_start_marker":
                            yield f"data: {json.dumps({'type': 'thinking_on', 'id': current_id})}\n\n"
                        elif item_type == "think_end_marker":
                            yield f"data: {json.dumps({'type': 'thinking_off', 'id': current_id})}\n\n"
                        elif item_type == "content_chunk":
                            content = stream_item.get("content", "")
                            if content:
                                next_response_text += content  # Only accumulate for this iteration
                                yield f"data: {json.dumps({'type': 'chunk', 'content': content, 'id': current_id})}\n\n"
                        else:
                            import logging
                            logger = logging.getLogger(__name__)
                            logger.warning(f"Unexpected stream item type in iteration {iteration_count}: {item_type}")

                    except json.JSONDecodeError:
                        import logging
                        logger = logging.getLogger(__name__)
                        logger.error(f"JSONDecodeError in iteration {iteration_count} stream: {stream_item_json_str}")
                        continue

                self.log(title=f"Iteration {iteration_count} LLM Response", message=next_response_text)

                # Set current_response to this iteration's fresh response only
                current_response = next_response_text

            # Check if we hit max iterations
            if iteration_count >= max_iterations:
                if self._is_intermediate_sql(current_response):
                    # LLM still wants to continue but we've hit the limit
                    yield f"data: {json.dumps({'type': 'intermediate_notification', 'id': current_id, 'message': f'🔄 Maximum intermediate SQL iterations ({max_iterations}) reached. Calling specialized assistant to generate final query based on all {len(intermediate_queries)} intermediate results...'})}\n\n"

                    # Call assistant to generate final SQL based on all accumulated context
                    final_sql = yield from self._generate_final_sql_with_assistant(
                        question=question,
                        intermediate_queries=intermediate_queries,
                        intermediate_results=intermediate_results,
                        initial_prompt_config=initial_prompt_config,
                        question_sql_list=question_sql_list,
                        ddl_list=ddl_list,
                        doc_list=doc_list,
                        current_id=current_id,
                        **kwargs
                    )

                    if not final_sql:
                        yield f"data: {json.dumps({'type': 'error', 'id': current_id, 'error': f'Assistant could not generate valid final SQL after {max_iterations} intermediate iterations. Please refine your question.', 'stream_end': True})}\n\n"
                        return
                else:
                    # We exited normally, current_response should contain final SQL
                    final_sql = self.extract_sql(current_response)
            else:
                # Normal exit, current_response should contain the final SQL
                final_sql = self.extract_sql(current_response)

            # Log context details for final generation
            context_summary = []
            context_summary.append(f"Total intermediate queries: {len(intermediate_queries)}")
            context_summary.append(f"Chat history entries: {len(self.chat_history)}")
            context_summary.append(f"Question-SQL examples: {len(question_sql_list)}")
            context_summary.append(f"DDL tables: {len(ddl_list)}")
            context_summary.append(f"Documentation items: {len(doc_list) + len(intermediate_queries)}")

            self.log(title="Final SQL Context Summary", message="\n".join(context_summary))

            # Notify user about final SQL generation
            yield f"data: {json.dumps({'type': 'final_sql_generation_complete', 'id': current_id, 'message': f'Final query generated after {len(intermediate_queries)} intermediate step(s).'})}\n\n"

            if self.is_sql_valid(final_sql):
                # Cache all intermediate and final SQL
                from main import get_cache
                cache = get_cache()
                if cache:
                    # Cache all intermediate queries and results
                    for i, (query, result) in enumerate(zip(intermediate_queries, intermediate_results)):
                        cache.set(id=current_id, field=f'intermediate_sql_{i+1}', value=query)
                        cache.set(id=current_id, field=f'intermediate_df_{i+1}', value=result)

                    # Cache final SQL
                    cache.set(id=current_id, field='sql', value=final_sql)
                    cache.set(id=current_id, field='intermediate_count', value=len(intermediate_queries))

                # Return final SQL result with enhanced metadata
                try:
                    yield f"data: {json.dumps({'type': 'sql_result', 'id': current_id, 'text': final_sql, 'full_response': current_response, 'intermediate_queries': intermediate_queries, 'intermediate_results_count': [len(df) for df in intermediate_results], 'used_intermediate_sql': True, 'intermediate_count': len(intermediate_queries), 'stream_end': True})}\n\n"
                except Exception as e:
                    self.log(title="Final SQL Result Serialization Error", message=f"Error serializing final SQL result: {str(e)}")
                    # Fallback without intermediate_queries if they contain non-serializable data
                    yield f"data: {json.dumps({'type': 'sql_result', 'id': current_id, 'text': final_sql, 'full_response': current_response, 'used_intermediate_sql': True, 'intermediate_count': len(intermediate_queries), 'stream_end': True})}\n\n"
            else:
                yield f"data: {json.dumps({'type': 'error', 'id': current_id, 'error': 'Could not generate valid final SQL from intermediate results', 'intermediate_queries': intermediate_queries, 'stream_end': True})}\n\n"

        except Exception as e:
            error_msg = f"Error processing intermediate SQL: {str(e)}"
            self.log(title="Intermediate SQL Processing Error", message=error_msg)
            yield f"data: {json.dumps({'type': 'error', 'id': current_id, 'error': error_msg, 'stream_end': True})}\n\n"

    def _generate_final_sql_with_assistant(self, question: str, intermediate_queries: list,
                                         intermediate_results: list, initial_prompt_config: str,
                                         question_sql_list: list, ddl_list: list, doc_list: list,
                                         current_id: str, **kwargs):
        """
        Generate final SQL using assistant when max intermediate iterations are reached.
        This method creates a specialized prompt that forces the LLM to generate final SQL
        based on all accumulated intermediate context.
        """
        try:
            self.log(title="Assistant Final SQL Generation", message=f"Generating final SQL with assistant after {len(intermediate_queries)} intermediate queries")

            # Build accumulated context from all intermediate results
            accumulated_doc_list = list(doc_list) if doc_list else []
            for i, (query, result) in enumerate(zip(intermediate_queries, intermediate_results), 1):
                intermediate_doc = f"The following is a pandas DataFrame with the results of intermediate SQL query #{i} ({query}): \n" + result.to_markdown()
                accumulated_doc_list.append(intermediate_doc)

            # Create a specialized prompt that forces final SQL generation
            system_prompt_content = (
                f"You are a versatile AI assistant. You have executed {len(intermediate_queries)} intermediate SQL queries to explore the database. "
                f"You have reached the maximum number of intermediate iterations and MUST now generate the final SQL query.\n\n"
                f"CONTEXT:\n- Original question: {question}\n"
            )

            # Add information about all intermediate queries
            for i, (query, result) in enumerate(zip(intermediate_queries, intermediate_results), 1):
                result_count = len(result) if hasattr(result, '__len__') else 0
                system_prompt_content += f"- Intermediate SQL #{i}: {query}\n"
                system_prompt_content += f"- Intermediate result #{i}: {result_count} rows of data\n"

            system_prompt_content += (
                f"\nFINAL SQL GENERATION INSTRUCTIONS:\n"
                f"Based on all the intermediate exploration above, you MUST generate the final SQL query that answers the original question.\n"
                f"You cannot generate more intermediate queries - this is the final step.\n"
                f"Use all the knowledge gained from the intermediate results to create an accurate and comprehensive final query.\n\n"
                f"REQUIREMENTS:\n"
                f"- Generate ONLY the final SQL query that answers: {question}\n"
                f"- Use insights from all {len(intermediate_queries)} intermediate queries\n"
                f"- Make the query optimized and comprehensive\n"
                f"- Ensure the query is PostgreSQL-compliant and executable\n"
                f"- Respond with ONLY the SQL query - no explanations, no additional text\n"
            )

            # Add DDL and documentation context
            system_prompt_content = self.add_ddl_to_prompt(system_prompt_content, ddl_list, max_tokens=self.max_tokens)
            system_prompt_content = self.add_documentation_to_prompt(system_prompt_content, accumulated_doc_list, max_tokens=self.max_tokens)
            # Add question-SQL pairs to the system prompt
            system_prompt_content = self.add_sql_to_prompt(system_prompt_content, question_sql_list, max_tokens=self.max_tokens)

            # Build message log
            message_log = [self.system_message(system_prompt_content)]

            # Add chat history
            if self.chat_history:
                for entry in self.chat_history:
                    if isinstance(entry, dict) and 'role' in entry and 'content' in entry:
                        message_log.append(entry)

            # Question-SQL pairs are included in the system prompt above via add_sql_to_prompt,
            # so we don't add them as separate messages in the chat history

            # Add final instruction
            user_content = f"Generate the final SQL query to answer: {question}\n\nIMPORTANT: Respond with ONLY the SQL query - no explanations."
            message_log.append(self.user_message(user_content))

            self.log(title="Assistant Final SQL Prompt", message=json.dumps(message_log, indent=2))

            # Notify user about assistant generation
            yield f"data: {json.dumps({'type': 'final_sql_generation_complete', 'id': current_id, 'message': f'Assistant generating final query based on {len(intermediate_queries)} intermediate results...'})}\n\n"

            # Generate final SQL with streaming
            llm_stream = self.submit_prompt(message_log, stream=True, **kwargs)
            final_response_text = ""

            for stream_item_json_str in llm_stream:
                try:
                    stream_item = json.loads(stream_item_json_str)
                    item_type = stream_item.get("type")

                    if item_type == "think_start_marker":
                        yield f"data: {json.dumps({'type': 'thinking_on', 'id': current_id})}\n\n"
                    elif item_type == "think_end_marker":
                        yield f"data: {json.dumps({'type': 'thinking_off', 'id': current_id})}\n\n"
                    elif item_type == "content_chunk":
                        content = stream_item.get("content", "")
                        if content:
                            final_response_text += content
                            yield f"data: {json.dumps({'type': 'chunk', 'content': content, 'id': current_id})}\n\n"
                    else:
                        import logging
                        logger = logging.getLogger(__name__)
                        logger.warning(f"Unexpected stream item type in assistant final SQL generation: {item_type}")

                except json.JSONDecodeError:
                    import logging
                    logger = logging.getLogger(__name__)
                    logger.error(f"JSONDecodeError in assistant final SQL stream: {stream_item_json_str}")
                    continue

            self.log(title="Assistant Final SQL Response", message=final_response_text)

            # Extract and validate final SQL
            final_sql = self.extract_sql(final_response_text)
            if self.is_sql_valid(final_sql):
                self.log(title="Assistant Final SQL Success", message=f"Generated valid final SQL: {final_sql}")
                return final_sql
            else:
                self.log(title="Assistant Final SQL Failed", message=f"Could not extract valid SQL from response: {final_response_text}")
                return None

        except Exception as e:
            error_msg = f"Error in assistant final SQL generation: {str(e)}"
            self.log(title="Assistant Final SQL Error", message=error_msg)
            yield f"data: {json.dumps({'type': 'error', 'id': current_id, 'error': error_msg, 'stream_end': True})}\n\n"
            return None

    def get_final_sql_prompt(self, initial_prompt_text: str, question: str,
                           intermediate_queries: list = None, intermediate_results: list = None,
                           intermediate_sql: str = None, # For backward compatibility
                           question_sql_list: list = None, ddl_list: list = None,
                           doc_list: list = None, chat_history: list = None,
                           iteration_count: int = 0, **kwargs):
        """Generate optimized prompt for final SQL generation after intermediate SQL execution(s)"""

        # Handle backward compatibility - convert single intermediate to list format
        if intermediate_sql is not None and intermediate_queries is None:
            intermediate_queries = [intermediate_sql]
            intermediate_results = [intermediate_results] if intermediate_results is not None else []

        # Ensure we have lists
        intermediate_queries = intermediate_queries or []
        intermediate_results = intermediate_results or []

        # Create optimized system prompt that allows LLM to choose next step
        if len(intermediate_queries) == 1:
            system_prompt_content = (
                f"You are a versatile AI assistant. Previously, you executed 1 intermediate SQL query to explore the available data. "
                f"Now you need to decide the next step based on the original question and the intermediate results.\n\n"
            )
        else:
            system_prompt_content = (
                f"You are a versatile AI assistant. Previously, you executed {len(intermediate_queries)} intermediate SQL queries to explore the available data. "
                f"Now you need to decide the next step based on the original question and all the intermediate results.\n\n"
            )

        system_prompt_content += f"CONTEXT:\n- Original question: {question}\n"

        # Add information about all intermediate queries
        for i, (query, result) in enumerate(zip(intermediate_queries, intermediate_results), 1):
            result_count = len(result) if hasattr(result, '__len__') else 0
            system_prompt_content += f"- Intermediate SQL #{i}: {query}\n"
            system_prompt_content += f"- Intermediate result #{i}: {result_count} rows of data\n"

        system_prompt_content += (
            f"\nDECISION INSTRUCTIONS:\n"
            f"You have TWO options to choose from:\n\n"
            f"OPTION 1 - Continue with more intermediate exploration:\n"
            f"- If you need more information to answer the original question accurately\n"
            f"- If the current intermediate results don't provide enough context\n"
            f"- If you need to explore additional tables, relationships, or data patterns\n"
            f"- Generate another intermediate SQL query by including 'intermediate_sql' in your response\n\n"
            f"OPTION 2 - Generate the final answer query:\n"
            f"- If you have sufficient information from the intermediate results to answer the original question\n"
            f"- If the intermediate data provides all the context needed for an accurate final query\n"
            f"- Generate ONLY the final SQL query that directly answers the user's question - no explanations\n\n"
            f"GUIDELINES:\n"
            f"- Carefully evaluate whether the current intermediate results are sufficient\n"
            f"- Consider the complexity and requirements of the original question\n"
            f"- Use the intermediate results to inform your decision and query generation\n"
            f"- Ensure your choice leads to the most accurate and complete answer\n"
            f"- If generating final SQL, make it optimized and comprehensive - respond with ONLY the SQL query\n"
            f"- If generating intermediate SQL, focus on gathering the missing information\n"
        )

        # Use the same structure as get_sql_prompt - build system message with context
        system_prompt_content = self.add_ddl_to_prompt(system_prompt_content, ddl_list, max_tokens=self.max_tokens)

        # Add documentation context (including intermediate results)
        current_doc_list = list(doc_list) if doc_list is not None else []
        system_prompt_content = self.add_documentation_to_prompt(system_prompt_content, current_doc_list, max_tokens=self.max_tokens)

        # Add question-SQL pairs to the system prompt
        system_prompt_content = self.add_sql_to_prompt(system_prompt_content, question_sql_list, max_tokens=self.max_tokens)

        # Add response guidelines that allow LLM to choose next step
        if len(intermediate_queries) == 1:
            guidelines = (
                "\n===DECISION GUIDELINES===\n"
                "0. EVALUATION: You have executed 1 intermediate SQL query to explore the data. \n"
                "1. DECISION POINT: Choose whether to continue exploration or generate the final answer. \n"
                "2. CONTINUE EXPLORATION: If you need more data, include 'intermediate_sql' in your response with another query. \n"
                "3. FINAL ANSWER: If you have enough information, generate ONLY the complete SQL query that answers the original question - no explanations. \n"
                "4. POSTGRESQL COMPLIANT: Ensure any SQL output is PostgreSQL-compliant and executable. \n"
                "5. CLEAR CHOICE: Make a clear decision based on whether the intermediate results are sufficient. \n"
                "6. QUALITY FOCUS: Choose the option that will lead to the most accurate and complete answer. \n"
                "7. SQL ONLY: When generating final SQL, respond with ONLY the SQL query - no explanations, no descriptions, no additional text. \n"
            )
        else:
            guidelines = (
                "\n===DECISION GUIDELINES===\n"
                f"0. EVALUATION: You have executed {len(intermediate_queries)} intermediate SQL queries to explore the data. \n"
                "1. DECISION POINT: Choose whether to continue exploration or generate the final answer. \n"
                "2. CONTINUE EXPLORATION: If you need more data, include 'intermediate_sql' in your response with another query. \n"
                "3. FINAL ANSWER: If you have enough information, generate ONLY the complete SQL query that answers the original question - no explanations. \n"
                "4. POSTGRESQL COMPLIANT: Ensure any SQL output is PostgreSQL-compliant and executable. \n"
                "5. CLEAR CHOICE: Make a clear decision based on whether ALL intermediate results provide sufficient context. \n"
                "6. QUALITY FOCUS: Choose the option that will lead to the most accurate and complete answer. \n"
                "7. SQL ONLY: When generating final SQL, respond with ONLY the SQL query - no explanations, no descriptions, no additional text. \n"
            )
        system_prompt_content += guidelines

        # Build message log like get_sql_prompt
        message_log = [self.system_message(system_prompt_content)]

        # Add chat history like get_sql_prompt
        if chat_history:
            for entry in chat_history:
                if isinstance(entry, dict) and 'role' in entry and 'content' in entry:
                    message_log.append(entry)

        # Question-SQL pairs are included in the system prompt above via add_sql_to_prompt,
        # so we don't add them as separate messages in the chat history

        # Add the user question with choice instruction (avoid duplicate if already in chat history)
        user_content = f"Based on the intermediate results, choose your next step:\n1. If you need more information, generate another intermediate SQL query (include 'intermediate_sql' in your response)\n2. If you have sufficient information, generate ONLY the final SQL query to answer this question: {question}\n\nIMPORTANT: If generating final SQL, respond with ONLY the SQL query - no explanations, no additional text."
        message_log.append(self.user_message(user_content))

        return message_log

    def get_sql_prompt(self, initial_prompt_text: str = None, initial_prompt: str = None, question: str = None, question_sql_list: list = None,
                      ddl_list: list = None, doc_list: list = None, chat_history: list = None, **kwargs):
        """Generate SQL prompt with context"""
        # Handle both parameter names for compatibility with base class
        system_prompt_content = initial_prompt_text or initial_prompt
        if system_prompt_content is None:
             system_prompt_content = (
                f"You are a helpful AI assistant for a hospital database system. Your primary goal is to generate {self.dialect} SQL queries based on user questions and the provided context (table DDLs, documentation, and question-SQL pairs). "
                "However, if a user's question is not related to SQL generation (e.g., a greeting like 'Hi', 'Hello', 'What can you do?', or general conversation), "
                "you should provide a brief, friendly, conversational response that explains you can help with finding patient or doctor information, consultations, and more. "
                "For greetings, keep your response simple and welcoming - do not mention SQL, PostgreSQL, or technical details. "
                "IMPORTANT: If asked about your name or identity (e.g., 'What's your name?', 'Who are you?'), do not mention any other model names or companies. "
                "Your SQL responses should ONLY be based on the given context and follow the response guidelines and format instructions."
            )
        
        system_prompt_content = self.add_ddl_to_prompt(system_prompt_content, ddl_list, max_tokens=self.max_tokens)

        static_doc = self.static_documentation
        current_doc_list = list(doc_list) if doc_list is not None else []
        if static_doc != "":
            current_doc_list.append(static_doc)

        system_prompt_content = self.add_documentation_to_prompt(system_prompt_content, current_doc_list, max_tokens=self.max_tokens)

        # Add question-SQL pairs to the prompt
        system_prompt_content = self.add_sql_to_prompt(system_prompt_content, question_sql_list, max_tokens=self.max_tokens)
        
        system_prompt_content += (
            "\n===Response Guidelines \n"
            "0. GREETINGS & CASUAL QUESTIONS: If the user says 'Hi', 'Hello', 'What can you do?', or similar greetings, respond with a brief, friendly message. Do NOT mention SQL, PostgreSQL, or technical details in greeting responses. \n"
            "0.1. IDENTITY QUESTIONS: If asked about your name or identity (e.g., 'What's your name?', 'Who are you?'), respond EXACTLY with 'I'm your hospital assistant. How can I help?' - do not mention any other model names, companies, or technical details. \n"
            "1. If the question is not related to generating SQL (e.g., a greeting, chit-chat, or general knowledge question), answer it directly and conversationally without generating SQL. \n"
            "2. If the provided context is sufficient, generate ONLY a valid SQL query without any explanations, comments, or additional text. \n"
            "3. Generate intermediate SQL queries when you need to explore the database before answering the main question. Use intermediate SQL in these scenarios:\n"
            "   a) COLUMN VALUES: When you need to know specific values, patterns, or distinct entries in a column (e.g., 'What are the distinct values that saved in the database?', 'Show me department names')\n"
            "   b) DATA EXPLORATION: When you need to understand the structure, relationships, or content of tables before writing the final query\n"
            "   c) VALIDATION: When you need to verify if certain records, relationships, or conditions exist in the database\n"
            "   d) COMPLEX JOINS: When you need to explore foreign key relationships or understand how tables connect\n"
            "   e) AGGREGATION PREP: When you need to understand data distribution before performing complex aggregations or calculations\n"
            "   f) DATE/TIME RANGES: When you need to explore available date ranges, time periods, or temporal patterns in the data\n"
            "   g) CONDITIONAL LOGIC: When you need to understand data patterns to build proper WHERE clauses or CASE statements\n"
            "   h) PERFORMANCE OPTIMIZATION: When you need to check data volume or distribution to optimize the final query\n"
            "   Always prepend intermediate queries with a comment saying 'intermediate_sql' and provide no explanation.\n"
            "4. If the provided context is insufficient, please explain why it can't be generated. \n"
            "5. Please use the most relevant table(s). \n"
            "6. If the question has been asked and answered before, please repeat the answer exactly as it was given before. \n"
            f"7. Ensure that the output SQL is {self.dialect}-compliant and executable, and free of syntax errors. \n"
            "8. IMPORTANT: When generating SQL, respond with ONLY the SQL query - no explanations, no descriptions, no additional text. \n"
            "9. Respond in the language of the user's question. \n"
            "10. Only generate a single SQL query. If you need to generate multiple queries, use intermediate SQL as described above. \n"
            
        )

        message_log = [self.system_message(system_prompt_content)]

        if chat_history:
            for entry in chat_history:
                if isinstance(entry, dict) and 'role' in entry and 'content' in entry:
                    message_log.append(entry)

        # Question-SQL pairs are now included in the system prompt above,
        # so we don't add them as separate messages in the chat history

        # Only add the current question if it's not already in chat history
        # (to avoid duplication when chat history already contains the current conversation)
        if not chat_history or not any(
            entry.get('role') == 'user' and entry.get('content') == question
            for entry in chat_history[-2:]  # Check last 2 entries
        ):
            message_log.append(self.user_message(question))

        return message_log
    
    def should_generate_chart_with_context(
        self, question: str, sql: str, df: pd.DataFrame, is_intermediate_sql: bool = False, **kwargs
    ) -> Tuple[bool, str]:
        """
        Uses the LLM to determine if a chart should be generated based on the context of the query and DataFrame.
        Returns a tuple of (should_generate, plotly_code).

        Args:
            question (str): The original question that was asked
            sql (str): The SQL query that was executed
            df (pd.DataFrame): The DataFrame with results
            is_intermediate_sql (bool): Whether this is for intermediate SQL results

        Returns:
            tuple: (should_generate: bool, plotly_code: str)
        """
        # Note: We removed the blanket ban on intermediate SQL results
        # Final SQL results that used intermediate exploration should still be eligible for charts

        if df.empty or len(df) <= 1 and df.shape[1] <= 1:
            return False, ""
        
        # Create metadata about the DataFrame
        try:
            # Get column information
            df_cols_info = []
            for col in df.columns:
                col_type = str(df[col].dtype)
                sample_values = df[col].dropna().head(3).tolist()
                df_cols_info.append(f"'{col}' (type: {col_type}, examples: {sample_values})")
            
            # Additional metadata about the DataFrame
            metadata = {
                "shape": f"{df.shape[0]} rows x {df.shape[1]} columns",
                "columns": df_cols_info,
                "numeric_columns": df.select_dtypes(include=['number']).columns.tolist(),
                "categorical_columns": df.select_dtypes(include=['object', 'category']).columns.tolist(),
                "date_columns": df.select_dtypes(include=['datetime']).columns.tolist(),
                "nunique": {col: df[col].nunique() for col in df.columns},
            }
            
            # Import config to check if we should include full dataframe data
            try:
                from backend.config import Config
                config = Config()
            except ImportError:
                from config import Config
                config = Config()

            df_metadata = f"DataFrame info: {metadata['shape']}\\n"
            df_metadata += "Column details:\\n" + "\\n".join(metadata['columns'])

            # Include actual dataframe data if configured to do so
            if config.USE_FULL_DATAFRAME_CONTEXT:
                max_rows = min(len(df), config.MAX_CONTEXT_ROWS)
                max_cols = min(len(df.columns), config.MAX_CONTEXT_COLUMNS)

                # Select data to include
                if len(df.columns) > max_cols:
                    df_for_chart = df.iloc[:max_rows, :max_cols]
                    column_note = f" (showing first {max_cols} of {len(df.columns)} columns)"
                else:
                    df_for_chart = df.head(max_rows)
                    column_note = ""

                data_description = f"Full data{column_note}" if max_rows == len(df) else f"Data (showing {max_rows} of {len(df)} rows{column_note})"
                df_metadata += f"\\n\\n{data_description}:\\n{df_for_chart.to_markdown(index=False)}"

            # Create the system prompt
            system_prompt = f"""
You are a data visualization assistant that decides whether to create a chart based on the query context and data.

The user asked this question: '{question}'

The question was answered with this SQL query: {sql}

{df_metadata}

Your task:
1. FIRST, analyze if this data would benefit from visualization. Consider these factors:
   - Is the data asking for trends, patterns, distributions, comparisons, or relationships?
   - Does the question imply a visual analysis would be helpful?
   - Is there enough data for meaningful visualization (e.g., not just a single value)?
   - Are there appropriate numeric or categorical columns for visualization?

2. THEN, based on your analysis:
   - If visualization would be valuable, respond with "VISUALIZATION: YES" followed by appropriate Plotly code
   - If visualization would NOT be valuable, respond with "VISUALIZATION: NO" without any explanation
   
When providing Plotly code, include ONLY pure Python code that creates a 'fig' object using either plotly.graph_objects or plotly.express.
Assume 'df' is already available. Don't include imports or print statements - just the visualization code.

IMPORTANT: You have access to the actual data values above. When creating charts, you can either:
1. Use the DataFrame directly: fig = px.bar(df, x='column1', y='column2')
2. Use actual data values directly when appropriate: fig = px.bar(x=['A', 'B', 'C'], y=[10, 20, 15])

Choose the approach that creates the most accurate and meaningful visualization based on the data provided.

For example, good Plotly code looks like:
```python
fig = px.bar(df, x='category', y='value')
fig.update_layout(title='Sample Plot')
```
"""

            # User prompt is a simple request to analyze
            user_prompt = "Based on the query context and dataframe details, should I create a chart? If yes, provide the appropriate Plotly code."
            
            print("\n" + "📊" * 20)
            print("📈 CHART GENERATION DECISION REQUEST")
            print("📊" * 20)
            print(f"❓ Original Question: {question}")
            print(f"📊 SQL Query: {sql}")
            print(f"📏 DataFrame shape: {df.shape}")
            print(f"📋 DataFrame columns: {list(df.columns)}")
            print(f"🔢 Numeric columns: {metadata['numeric_columns']}")
            print(f"📝 Categorical columns: {metadata['categorical_columns']}")
            print("📊" * 20 + "\n")
            
            message_log = [
                self.system_message(system_prompt),
                self.user_message(user_prompt)
            ]
            
            response = self.submit_prompt(message_log, **kwargs)
            
            # Parse the response
            if "VISUALIZATION: YES" in response:
                plotly_code_part = response.split("VISUALIZATION: YES", 1)[1].strip()
                # Extract the Python code from the response
                plotly_code = self._sanitize_plotly_code(self._extract_python_code(plotly_code_part))
                return True, plotly_code
            else:
                # If the LLM decides no chart is needed
                return False, ""
                
        except Exception as e:
            self.log(title="Chart Decision Error", message=f"Error determining if chart should be generated: {str(e)}")
            return False, ""

    def generate_summary_stream(self, question: str, df: pd.DataFrame, current_id: str, **kwargs):
        """
        Generate a streaming summary response using Server-Sent Events format.
        
        Args:
            question (str): The question that was asked.
            df (pd.DataFrame): The results of the SQL query.
            current_id (str): Unique identifier for this request.
            **kwargs: Additional arguments passed to submit_prompt.
            
        Yields:
            str: Server-Sent Events formatted data chunks.
        """
        try:
            # Limit dataframe size for summary to avoid token limits
            df_for_summary = df.head(10) if len(df) > 10 else df
            
            message_log = [
                self.system_message(
                    f"/no_thinkYou are a helpful data assistant. The user asked the question: '{question}'\n\n"
                    f"The following is a pandas DataFrame with the results of the query: \n{df_for_summary.to_markdown()}\n\n"
                ),
                self.user_message(
                    "Briefly summarize the data based on the question that was asked. Do not respond with any additional explanation beyond the summary. Respond in the language of the user's question."
                ),
            ]

            print("\n" + "📊" * 20)
            print("📈 SUMMARY GENERATION REQUEST")
            print("📊" * 20)
            print(f"❓ Original Question: {question}")
            print(f"📏 DataFrame shape: {df_for_summary.shape}")
            print(f"📋 DataFrame columns: {list(df_for_summary.columns)}")
            print("📊" * 20 + "\n")

            self.log(title="Summary Generation Prompt", message=json.dumps(message_log, indent=2, ensure_ascii=False))

            llm_stream = self.submit_prompt(message_log, stream=True, **kwargs)
            
            buffer = []
            for stream_item_json_str in llm_stream:
                try:
                    stream_item = json.loads(stream_item_json_str)
                    item_type = stream_item.get("type")
                    
                    if item_type == "think_start_marker":
                        yield f"data: {json.dumps({'type': 'thinking_on', 'id': current_id})}\n\n"
                    elif item_type == "think_end_marker":
                        yield f"data: {json.dumps({'type': 'thinking_off', 'id': current_id})}\n\n"
                    elif item_type == "content_chunk":
                        content = stream_item.get("content", "")
                        if content:
                            buffer.append(content)
                            yield f"data: {json.dumps({'type': 'summary_chunk', 'content': content, 'id': current_id})}\n\n"
                    else:
                        import logging
                        logger = logging.getLogger(__name__)
                        logger.warning(f"Unexpected stream item type in summary generation: {item_type}")

                except json.JSONDecodeError:
                    import logging
                    logger = logging.getLogger(__name__)
                    logger.error(f"JSONDecodeError in summary stream: {stream_item_json_str}")
                    continue

            final_summary = "".join(buffer)
            self.log(title="Final Summary Generated", message=final_summary)

            # Add summary to chat history using helper method
            self.add_summary_to_chat_history(final_summary, current_id)
            
            # Cache the final summary
            from main import get_cache
            cache = get_cache()
            if cache:
                cache.set(id=current_id, field='summary', value=final_summary)

            yield f"data: {json.dumps({'type': 'summary_result', 'id': current_id, 'summary': final_summary, 'stream_end': True})}\n\n"

        except Exception as e:
            error_msg = f"Error generating summary: {str(e)}"
            self.log(title="Summary Generation Error", message=error_msg)
            yield f"data: {json.dumps({'type': 'error', 'id': current_id, 'error': error_msg, 'stream_end': True})}\n\n"


    def add_summary_to_chat_history(self, summary: str, cache_id: str = None):
        """
        Add a summary message to chat history with proper formatting.

        Args:
            summary (str): The summary text to add
            cache_id (str, optional): Cache ID to update cached chat history
        """
        if summary and summary.strip():
            # Format summary with consistent prefix
            formatted_summary = f"[SUMMARY] {summary.strip()}"
            summary_message = self.assistant_message(formatted_summary)
            self.chat_history.append(summary_message)

            # Update cache if cache_id provided
            if cache_id:
                try:
                    from main import get_cache
                    cache = get_cache()
                    if cache:
                        cache.set(id=cache_id, field='chat_history', value=self.chat_history.copy())
                except Exception as e:
                    self.log(title="Cache Update Error", message=f"Failed to update chat history cache: {str(e)}")

    def add_dataframe_to_chat_history(self, sql: str, df: pd.DataFrame, cache_id: str = None, is_intermediate: bool = True):
        """
        Add dataframe results to chat history for model context.

        Args:
            sql (str): The SQL query that was executed
            df (pd.DataFrame): The dataframe results
            cache_id (str, optional): Cache ID to update cached chat history
            is_intermediate (bool): Whether this is an intermediate SQL query (default: True)
        """
        if df is not None and not df.empty:
            # Import config to check if we should use full dataframe context
            try:
                from backend.config import Config
                config = Config()
            except ImportError:
                from config import Config
                config = Config()

            # Determine how much data to include in context
            if config.USE_FULL_DATAFRAME_CONTEXT:
                # Use full dataframe but respect limits
                max_rows = min(len(df), config.MAX_CONTEXT_ROWS)
                max_cols = min(len(df.columns), config.MAX_CONTEXT_COLUMNS)

                # Select columns if there are too many
                if len(df.columns) > max_cols:
                    df_for_context = df.iloc[:max_rows, :max_cols]
                    column_note = f" (showing first {max_cols} of {len(df.columns)} columns)"
                else:
                    df_for_context = df.head(max_rows)
                    column_note = ""

                data_description = f"Full data{column_note}" if max_rows == len(df) else f"Data (showing {max_rows} of {len(df)} rows{column_note})"
            else:
                # Use original sampling behavior
                df_for_context = df.head(5) if len(df) > 5 else df
                data_description = "Sample data"

            # Format the dataframe context with clear intermediate/final marking
            sql_type = "[INTERMEDIATE_SQL_RESULT]" if is_intermediate else "[FINAL_SQL_RESULT]"
            context_parts = [
                f"{sql_type} Executed SQL: {sql}",
                f"Result summary: {len(df)} rows, {len(df.columns)} columns",
                f"Columns: {', '.join(df.columns.tolist())}",
                f"{data_description}:\n{df_for_context.to_markdown(index=False)}"
            ]

            formatted_context = "\n".join(context_parts)
            context_message = self.assistant_message(formatted_context)
            self.chat_history.append(context_message)

            # Update cache if cache_id provided
            if cache_id:
                try:
                    from main import get_cache
                    cache = get_cache()
                    if cache:
                        cache.set(id=cache_id, field='chat_history', value=self.chat_history.copy())
                except Exception as e:
                    self.log(title="Cache Update Error", message=f"Failed to update dataframe context cache: {str(e)}")
        else:
            # Add empty result context with proper marking
            sql_type = "[INTERMEDIATE_SQL_RESULT]" if is_intermediate else "[FINAL_SQL_RESULT]"
            context_message = self.assistant_message(f"{sql_type} Executed SQL: {sql}\nResult: No data returned (0 rows)")
            self.chat_history.append(context_message)

            # Update cache if cache_id provided
            if cache_id:
                try:
                    from main import get_cache
                    cache = get_cache()
                    if cache:
                        cache.set(id=cache_id, field='chat_history', value=self.chat_history.copy())
                except Exception as e:
                    self.log(title="Cache Update Error", message=f"Failed to update empty result context cache: {str(e)}")

    def generate_corrected_sql(self, question: str, sql: str, error_msg: str, **kwargs) -> Union[str, None]:
        """
        Generate a corrected SQL query based on the original question, failed SQL, error message,
        and additional database context (DDLs, documentation, similar Q-SQL pairs).
        
        Args:
            question (str): The original question that was asked
            sql (str): The SQL query that failed
            error_msg (str): The error message from the database
            **kwargs: Additional arguments passed to submit_prompt
            
        Returns:
            Union[str, None]: The corrected SQL query, or None if correction fails or is invalid.
        """
        try:
            self.log(title="SQL Correction Initiated", message=f"Attempting to correct SQL for question: '{question}'")
            
            # 1. Fetch Additional Context
            question_sql_list = self.get_similar_question_sql(question, **kwargs)
            if question_sql_list:
                question_sql_list = question_sql_list[:3]  # Limit for conciseness

            ddl_list = self.get_related_ddl(question, **kwargs)
            if ddl_list:
                ddl_list = ddl_list[:5] # Limit for conciseness

            doc_list = self.get_related_documentation(question, **kwargs)
            if doc_list:
                doc_list = doc_list[:2] # Limit for conciseness

            # 2. Construct the System Prompt
            system_prompt_parts = [
                f"You are a {self.dialect} expert. Your task is to fix a broken SQL query based on the error message and the provided context.",
                f"The original question was: '{question}'",
                f"The failed SQL query was:\n{sql}",
                f"The error message was:\n{error_msg}",
                "To help you, here is some context about the database:"
            ]

            # Add DDL to prompt
            if ddl_list:
                system_prompt_parts.append("\nRelevant Table DDLs:")
                for ddl_item in ddl_list:
                    system_prompt_parts.append(f"{ddl_item}")
            
            # Add Documentation to prompt
            current_doc_list = list(doc_list) if doc_list is not None else []
            if hasattr(self, 'static_documentation') and self.static_documentation:
                current_doc_list.append(self.static_documentation)
            
            if current_doc_list:
                system_prompt_parts.append("\nRelevant Documentation:")
                for doc_item in current_doc_list:
                    system_prompt_parts.append(f"{doc_item}")

            # Add Question-SQL examples to prompt
            if question_sql_list:
                system_prompt_parts.append("\nSimilar Question-SQL Examples:")
                for example in question_sql_list:
                    if example and "question" in example and "sql" in example:
                        system_prompt_parts.append(f"Q: {example['question']}\nA: {example['sql']}")
            
            system_prompt_parts.append(
                f"\nPlease analyze the error and the provided context to generate ONLY the corrected SQL query without any explanations."
                f"\nThe corrected query should:"
                f"\n1. Fix the specific error mentioned."
                f"\n2. Still answer the original question correctly, using the provided database context."
                f"\n3. Be syntactically correct and executable."
                f"\n4. Follow {self.dialect} standards."
                f"\n\nRespond with ONLY the corrected SQL query, no additional text or explanations."
            )
            
            system_prompt = "\n".join(system_prompt_parts)

            self.log(title="SQL Correction System Prompt", message=system_prompt)

            # 3. Create Message Log
            message_log = [
                self.system_message(system_prompt),
                self.user_message("Please provide the corrected SQL query.")
            ]

            # 4. Submit to LLM
            corrected_response = self.submit_prompt(message_log, **kwargs)
            
            # 5. Extract SQL
            if corrected_response is None: 
                self.log(title="SQL Correction Failed", message="LLM did not return a response for the correction.")
                return None

            corrected_sql = self.extract_sql(str(corrected_response))
            
            # 6. Validate SQL
            if corrected_sql and self.is_sql_valid(corrected_sql): 
                self.log(title="SQL Correction Success", message=f"Original SQL: {sql}\nCorrected SQL: {corrected_sql}")
                return corrected_sql
            else:
                log_msg = f"Could not generate valid corrected SQL. Original: {sql}. "
                if corrected_sql:
                    log_msg += f"Attempted Corrected SQL: {corrected_sql}. "
                else:
                    log_msg += "No SQL was extracted from LLM response. "
                log_msg += f"Full LLM Response: {corrected_response}"
                self.log(title="SQL Correction Failed - Invalid/No SQL", message=log_msg)
                return None

        # 7. Handle Exceptions
        except Exception as e:
            error_details = traceback.format_exc()
            self.log(title="SQL Correction System Error", message=f"Error during SQL correction process: {str(e)}\nTraceback:\n{error_details}")
            return None