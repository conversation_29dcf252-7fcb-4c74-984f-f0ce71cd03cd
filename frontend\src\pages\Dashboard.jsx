import React, { useState, useEffect } from 'react';
import StatCard from '../components/Dashboard/StatCard';
import Widget from '../components/Dashboard/Widget';
import Chart from '../components/Chat/Chart';
import LoadingSpinner from '../components/UI/LoadingSpinner';
import Modal from '../components/UI/Modal';
import PatientSearchModal from '../components/Modals/PatientSearchModal';
import ConsultationSearchModal from '../components/Modals/ConsultationSearchModal';
import DoctorSearchModal from '../components/Modals/DoctorSearchModal';
import { useTheme } from '../contexts/ThemeContext';

// Theme-aware color palette helper
const getChartColors = (isDark) => ({
  primary: isDark ? '#60A5FA' : '#3B82F6',
  secondary: isDark ? '#34D399' : '#10B981',
  accent: isDark ? '#FBBF24' : '#F59E0B',
  danger: isDark ? '#F87171' : '#EF4444',
  purple: isDark ? '#A78BFA' : '#8B5CF6',
  indigo: isDark ? '#818CF8' : '#6366F1',
  pink: isDark ? '#F472B6' : '#EC4899',
  teal: isDark ? '#2DD4BF' : '#14B8A6',
  text: isDark ? '#E5E7EB' : '#374151',
  textSecondary: isDark ? '#9CA3AF' : '#6B7280'
});

// Chart transformation functions
const transformPatientDemographics = (data, isDark = false) => {
  if (!data || !data.gender_distribution || !data.age_distribution) return null;

  const colors = getChartColors(isDark);

  return {
    data: [
      {
        values: data.gender_distribution.map(item => item.count),
        labels: data.gender_distribution.map(item => item.gender),
        type: 'pie',
        name: 'Gender Distribution',
        domain: { row: 0, column: 0 },
        marker: {
          colors: [colors.primary, colors.danger, colors.secondary, colors.accent],
          line: {
            color: isDark ? '#1F2937' : '#FFFFFF',
            width: 2
          }
        },
        textfont: { color: colors.text }
      },
      {
        x: data.age_distribution.map(item => item.age_group),
        y: data.age_distribution.map(item => item.count),
        type: 'bar',
        name: 'Age Distribution',
        xaxis: 'x2',
        yaxis: 'y2',
        marker: {
          color: colors.purple,
          line: {
            color: isDark ? '#1F2937' : '#FFFFFF',
            width: 1
          }
        }
      }
    ],
    layout: {
      title: {
        text: 'Patient Demographics',
        font: { color: colors.text, size: 16 }
      },
      grid: { rows: 1, columns: 2, pattern: 'independent' },
      annotations: [
        {
          text: 'Gender Distribution',
          x: 0.2,
          y: 1.1,
          xref: 'paper',
          yref: 'paper',
          showarrow: false,
          font: { size: 14, color: colors.text }
        },
        {
          text: 'Age Distribution',
          x: 0.8,
          y: 1.1,
          xref: 'paper',
          yref: 'paper',
          showarrow: false,
          font: { size: 14, color: colors.text }
        }
      ],
      xaxis2: {
        domain: [0.6, 1],
        title: { text: 'Age Group', font: { color: colors.text } },
        tickfont: { color: colors.textSecondary }
      },
      yaxis2: {
        domain: [0, 1],
        title: { text: 'Count', font: { color: colors.text } },
        tickfont: { color: colors.textSecondary }
      }
    }
  };
};

const transformConsultationTrends = (data, isDark = false) => {
  if (!data || !data.monthly_consultations || !data.consultations_by_specialty) return null;

  const colors = getChartColors(isDark);

  // Format month labels properly
  const monthLabels = data.monthly_consultations.map(item => {
    // Convert "2025-05" format to "May 2025"
    const [year, month] = item.month.split('-');
    const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
                       'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
    return `${monthNames[parseInt(month) - 1]} ${year}`;
  });

  return {
    data: [
      {
        x: monthLabels,
        y: data.monthly_consultations.map(item => item.count),
        type: 'bar',
        name: 'Monthly Consultations',
        marker: {
          color: colors.primary,
          line: {
            color: isDark ? '#1F2937' : '#FFFFFF',
            width: 1
          }
        }
      }
    ],
    layout: {
      title: {
        text: 'Monthly Consultation Trends',
        font: { color: colors.text, size: 16 }
      },
      xaxis: {
        title: { text: 'Month', font: { color: colors.text } },
        tickfont: { color: colors.textSecondary }
      },
      yaxis: {
        title: { text: 'Number of Consultations', font: { color: colors.text } },
        tickfont: { color: colors.textSecondary }
      },
      showlegend: false
    }
  };
};

const transformConsultationsBySpecialty = (data, isDark = false) => {
  if (!data || !data.consultations_by_specialty) return null;

  const colors = getChartColors(isDark);

  return {
    data: [
      {
        values: data.consultations_by_specialty.map(item => item.count),
        labels: data.consultations_by_specialty.map(item => item.specialty),
        type: 'pie',
        marker: {
          colors: [colors.secondary, colors.accent, colors.danger, colors.purple, colors.indigo, colors.pink],
          line: {
            color: isDark ? '#1F2937' : '#FFFFFF',
            width: 2
          }
        },
        textfont: { color: colors.text, size: 12 },
        textposition: 'auto',
        textinfo: 'label+percent'
      }
    ],
    layout: {
      title: {
        text: 'Consultations by Specialty',
        font: { color: colors.text, size: 16 }
      },
      showlegend: true,
      legend: {
        orientation: 'v',
        x: 1.02,
        y: 0.5,
        font: { color: colors.text }
      }
    }
  };
};

const transformDepartmentStats = (data, isDark = false) => {
  if (!data || !data.department_statistics) return null;

  const colors = getChartColors(isDark);
  const departments = data.department_statistics.map(item => item.department);
  const doctorCounts = data.department_statistics.map(item => item.doctor_count);
  const consultationCounts = data.department_statistics.map(item => item.consultation_count);
  const avgAges = data.department_statistics.map(item => item.avg_patient_age);

  return {
    data: [
      {
        x: departments,
        y: doctorCounts,
        type: 'bar',
        name: 'Number of Doctors',
        marker: {
          color: departments.map(() => '#10B981'), // Emerald green for all bars
          line: {
            color: isDark ? '#1F2937' : '#FFFFFF',
            width: 1
          }
        }
      },
      {
        x: departments,
        y: consultationCounts,
        type: 'bar',
        name: 'Consultations',
        marker: {
          color: departments.map(() => '#F59E0B'), // Amber/Orange for all bars
          line: {
            color: isDark ? '#1F2937' : '#FFFFFF',
            width: 1
          }
        }
      },
      {
        x: departments,
        y: avgAges,
        type: 'scatter',
        mode: 'lines+markers',
        name: 'Avg Patient Age',
        yaxis: 'y2',
        line: { color: colors.danger, width: 4 },
        marker: { size: 10, color: colors.danger, symbol: 'diamond' }
      }
    ],
    layout: {
      title: {
        text: 'Department Statistics',
        font: { color: colors.text, size: 16 }
      },
      xaxis: {
        title: { text: 'Department', font: { color: colors.text } },
        tickfont: { color: colors.textSecondary }
      },
      yaxis: {
        title: { text: 'Count', font: { color: colors.text } },
        side: 'left',
        tickfont: { color: colors.textSecondary }
      },
      yaxis2: {
        title: { text: 'Average Age (years)', font: { color: colors.text } },
        side: 'right',
        overlaying: 'y',
        showgrid: false,
        tickfont: { color: colors.textSecondary }
      },
      barmode: 'group'
    }
  };
};

const Dashboard = () => {
  const { isDark } = useTheme();

  // Modal state
  const [activeModal, setActiveModal] = useState(null);
  const [modalOrigin, setModalOrigin] = useState(null);
  const [originElement, setOriginElement] = useState(null);

  // Modal handlers
  const openModal = (modalType, position) => {
    setModalOrigin(position);
    setOriginElement(position.element);
    setActiveModal(modalType);
  };

  const closeModal = () => {
    setActiveModal(null);
    setModalOrigin(null);
    setOriginElement(null);
  };

  const [stats, setStats] = useState({
    totalPatients: '-',
    totalConsultations: '-',
    totalDoctors: '-',
    recentConsultations: '-'
  });
  const [widgets, setWidgets] = useState({
    patientDemographics: { loading: true, data: null, error: null },
    consultationTrends: { loading: true, data: null, error: null },
    departmentStats: { loading: true, data: null, error: null },
    recentActivity: { loading: true, data: null, error: null }
  });

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      // Load overview statistics
      await loadOverviewStats();
      
      // Load widget data
      await Promise.all([
        loadPatientDemographics(),
        loadConsultationTrends(),
        loadDepartmentStats(),
        loadRecentActivity()
      ]);
    } catch (error) {
      console.error('Error loading dashboard data:', error);
    }
  };

  const loadOverviewStats = async () => {
    try {
      const response = await fetch('/api/v1/dashboard/overview');
      const data = await response.json();
      
      if (response.ok) {
        setStats({
          totalPatients: data.data.total_patients || '-',
          totalConsultations: data.data.total_consultations || '-',
          totalDoctors: data.data.total_doctors || '-',
          recentConsultations: data.data.recent_consultations || '-'
        });
      }
    } catch (error) {
      console.error('Error loading overview stats:', error);
    }
  };

  const loadPatientDemographics = async () => {
    try {
      const response = await fetch('/api/v1/dashboard/patient-demographics');
      const data = await response.json();

      setWidgets(prev => ({
        ...prev,
        patientDemographics: {
          loading: false,
          data: response.ok ? data.data : null,
          error: response.ok ? null : data.error || 'Failed to load data'
        }
      }));
    } catch (error) {
      setWidgets(prev => ({
        ...prev,
        patientDemographics: {
          loading: false,
          data: null,
          error: 'Network error'
        }
      }));
    }
  };

  const loadConsultationTrends = async () => {
    try {
      const response = await fetch('/api/v1/dashboard/consultation-trends');
      const data = await response.json();

      setWidgets(prev => ({
        ...prev,
        consultationTrends: {
          loading: false,
          data: response.ok ? data.data : null,
          error: response.ok ? null : data.error || 'Failed to load data'
        }
      }));
    } catch (error) {
      setWidgets(prev => ({
        ...prev,
        consultationTrends: {
          loading: false,
          data: null,
          error: 'Network error'
        }
      }));
    }
  };

  const loadDepartmentStats = async () => {
    try {
      const response = await fetch('/api/v1/dashboard/department-stats');
      const data = await response.json();

      setWidgets(prev => ({
        ...prev,
        departmentStats: {
          loading: false,
          data: response.ok ? data.data : null,
          error: response.ok ? null : data.error || 'Failed to load data'
        }
      }));
    } catch (error) {
      setWidgets(prev => ({
        ...prev,
        departmentStats: {
          loading: false,
          data: null,
          error: 'Network error'
        }
      }));
    }
  };

  const loadRecentActivity = async () => {
    try {
      const response = await fetch('/api/v1/dashboard/recent-activity');
      const data = await response.json();
      
      setWidgets(prev => ({
        ...prev,
        recentActivity: {
          loading: false,
          data: response.ok ? data.data : null,
          error: response.ok ? null : data.error || 'Failed to load data'
        }
      }));
    } catch (error) {
      setWidgets(prev => ({
        ...prev,
        recentActivity: {
          loading: false,
          data: null,
          error: 'Network error'
        }
      }));
    }
  };

  const handleRefresh = () => {
    // Reset all widgets to loading state
    setWidgets({
      patientDemographics: { loading: true, data: null, error: null },
      consultationTrends: { loading: true, data: null, error: null },
      departmentStats: { loading: true, data: null, error: null },
      recentActivity: { loading: true, data: null, error: null }
    });
    
    // Reload data
    loadDashboardData();
  };

  return (
    <div className="p-8">
      {/* Header */}
      <div className="flex justify-between items-center mb-8">
        <h2 className="text-2xl font-semibold text-gray-900 dark:text-gray-100">
          <i className="fas fa-tachometer-alt mr-3"></i>
          System Dashboard
        </h2>
        
        <button
          onClick={handleRefresh}
          className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors flex items-center"
        >
          <i className="fas fa-sync-alt mr-2"></i>
          Refresh
        </button>
      </div>

      {/* Overview Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <StatCard
          title="Total Patients"
          value={stats.totalPatients}
          icon="fas fa-users"
          color="blue"
          clickable={true}
          isHidden={activeModal === 'patients'}
          onClick={(position) => openModal('patients', position)}
        />
        <StatCard
          title="Total Consultations"
          value={stats.totalConsultations}
          icon="fas fa-stethoscope"
          color="green"
          clickable={true}
          isHidden={activeModal === 'consultations'}
          onClick={(position) => openModal('consultations', position)}
        />
        <StatCard
          title="Total Doctors"
          value={stats.totalDoctors}
          icon="fas fa-user-md"
          color="purple"
          clickable={true}
          isHidden={activeModal === 'doctors'}
          onClick={(position) => openModal('doctors', position)}
        />
        <StatCard
          title="Recent Consultations (30 days)"
          value={stats.recentConsultations}
          icon="fas fa-calendar-alt"
          color="orange"
          clickable={true}
          isHidden={activeModal === 'consultations'}
          onClick={(position) => openModal('consultations', position)}
        />
      </div>

      {/* Dashboard Widgets */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Patient Demographics Widget */}
        <div className="lg:col-span-2">
          <Widget
            title="Patient Demographics"
            icon="fas fa-users"
            loading={widgets.patientDemographics.loading}
            error={widgets.patientDemographics.error}
          >
          {widgets.patientDemographics.data && (
            <div>
              <Chart data={transformPatientDemographics(widgets.patientDemographics.data, isDark)} />
              <div className="mt-4 grid grid-cols-2 gap-4 text-sm">
                <div className="bg-gray-50 dark:bg-gray-700 p-3 rounded">
                  <h4 className="font-semibold mb-2">Gender Distribution</h4>
                  {widgets.patientDemographics.data.gender_distribution?.map((item, index) => (
                    <div key={index} className="flex justify-between">
                      <span>{item.gender}:</span>
                      <span className="font-medium">{item.count}</span>
                    </div>
                  ))}
                </div>
                <div className="bg-gray-50 dark:bg-gray-700 p-3 rounded">
                  <h4 className="font-semibold mb-2">Age Distribution</h4>
                  {widgets.patientDemographics.data.age_distribution?.map((item, index) => (
                    <div key={index} className="flex justify-between">
                      <span>{item.age_group}:</span>
                      <span className="font-medium">{item.count}</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}
          </Widget>
        </div>

        {/* Monthly Consultation Trends Widget */}
        <Widget
          title="Monthly Consultation Trends"
          icon="fas fa-chart-line"
          loading={widgets.consultationTrends.loading}
          error={widgets.consultationTrends.error}
        >
          {widgets.consultationTrends.data && (
            <div>
              <Chart data={transformConsultationTrends(widgets.consultationTrends.data, isDark)} />
              <div className="mt-4">
                <div className="bg-gray-50 dark:bg-gray-700 p-3 rounded">
                  <h4 className="font-semibold mb-2">Monthly Data</h4>
                  <div className="grid grid-cols-1 gap-2">
                    {widgets.consultationTrends.data.monthly_consultations?.map((item, index) => {
                      const [year, month] = item.month.split('-');
                      const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
                                         'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
                      const formattedMonth = `${monthNames[parseInt(month) - 1]} ${year}`;
                      return (
                        <div key={index} className="flex justify-between">
                          <span>{formattedMonth}:</span>
                          <span className="font-medium">{item.count} consultations</span>
                        </div>
                      );
                    })}
                  </div>
                </div>
              </div>
            </div>
          )}
        </Widget>

        {/* Consultations by Specialty Widget */}
        <Widget
          title="Consultations by Specialty"
          icon="fas fa-user-md"
          loading={widgets.consultationTrends.loading}
          error={widgets.consultationTrends.error}
        >
          {widgets.consultationTrends.data && (
            <div>
              <Chart data={transformConsultationsBySpecialty(widgets.consultationTrends.data, isDark)} />
              <div className="mt-4">
                <div className="bg-gray-50 dark:bg-gray-700 p-3 rounded">
                  <h4 className="font-semibold mb-2">Specialty Breakdown</h4>
                  <div className="grid grid-cols-1 gap-2">
                    {widgets.consultationTrends.data.consultations_by_specialty?.map((item, index) => (
                      <div key={index} className="flex justify-between">
                        <span>{item.specialty}:</span>
                        <span className="font-medium">{item.count} consultations</span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          )}
        </Widget>

        {/* Department Statistics Widget */}
        <div className="lg:col-span-2">
          <Widget
            title="Department Statistics"
            icon="fas fa-building"
            loading={widgets.departmentStats.loading}
            error={widgets.departmentStats.error}
          >
          {widgets.departmentStats.data && (
            <div>
              <Chart data={transformDepartmentStats(widgets.departmentStats.data, isDark)} />
              <div className="mt-4">
                <div className="bg-gray-50 dark:bg-gray-700 p-3 rounded">
                  <h4 className="font-semibold mb-2">Department Details</h4>
                  <div className="overflow-x-auto">
                    <table className="w-full text-sm">
                      <thead>
                        <tr className="border-b border-gray-300 dark:border-gray-600">
                          <th className="text-left py-1">Department</th>
                          <th className="text-right py-1">Doctors</th>
                          <th className="text-right py-1">Consultations</th>
                          <th className="text-right py-1">Avg Age</th>
                        </tr>
                      </thead>
                      <tbody>
                        {widgets.departmentStats.data.department_statistics?.map((item, index) => (
                          <tr key={index} className="border-b border-gray-200 dark:border-gray-600">
                            <td className="py-1">{item.department}</td>
                            <td className="text-right py-1 font-medium">{item.doctor_count}</td>
                            <td className="text-right py-1 font-medium">{item.consultation_count}</td>
                            <td className="text-right py-1 font-medium">{item.avg_patient_age?.toFixed(1)}</td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            </div>
          )}
          </Widget>
        </div>

        {/* Recent Activity Widget */}
        <div className="lg:col-span-2">
        <Widget
          title="Recent Activity"
          icon="fas fa-clock"
          loading={widgets.recentActivity.loading}
          error={widgets.recentActivity.error}
        >
          {widgets.recentActivity.data && (
            <div className="space-y-3">
              <div className="mb-4 text-sm text-gray-600 dark:text-gray-400">
                Showing {widgets.recentActivity.data.length} recent activities
              </div>
              {widgets.recentActivity.data.slice(0, 10).map((activity, index) => (
                <div key={index} className="flex items-start p-3 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors">
                  <div className="flex-shrink-0 mr-3 mt-1">
                    <i className={`${activity.icon || 'fas fa-circle'} text-blue-500 text-lg`}></i>
                  </div>
                  <div className="flex-grow min-w-0">
                    <p className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-1">
                      {activity.description}
                    </p>
                    <div className="flex items-center justify-between">
                      <p className="text-xs text-gray-500 dark:text-gray-400">
                        <i className="fas fa-clock mr-1"></i>
                        {activity.timestamp}
                      </p>
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                        {activity.activity_type}
                      </span>
                    </div>
                  </div>
                </div>
              ))}
              {widgets.recentActivity.data.length === 0 && (
                <div className="text-center py-8 text-gray-500 dark:text-gray-400">
                  <i className="fas fa-inbox text-3xl mb-2"></i>
                  <p>No recent activity found</p>
                </div>
              )}
            </div>
          )}
          </Widget>
        </div>
      </div>

      {/* Modals */}
      <Modal
        isOpen={activeModal === 'patients'}
        onClose={closeModal}
        title="Patient Search"
        originPosition={modalOrigin}
        originElement={originElement}
        size="large"
      >
        <PatientSearchModal />
      </Modal>

      <Modal
        isOpen={activeModal === 'consultations'}
        onClose={closeModal}
        title="Consultation Search"
        originPosition={modalOrigin}
        originElement={originElement}
        size="large"
      >
        <ConsultationSearchModal />
      </Modal>

      <Modal
        isOpen={activeModal === 'doctors'}
        onClose={closeModal}
        title="Doctor Search"
        originPosition={modalOrigin}
        originElement={originElement}
        size="large"
      >
        <DoctorSearchModal />
      </Modal>
    </div>
  );
};

export default Dashboard;
