"""
Demonstration of Medical Record Chunking for Granular Embeddings

This script demonstrates how the new chunking feature improves search accuracy
by breaking medical records into focused sections that can be embedded separately.
"""

import os
import sys
from pathlib import Path
from medical_record_embedder import MedicalRecordEmbedder, MedicalRecord, MedicalRecordChunk
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def demonstrate_chunking():
    """Demonstrate the chunking functionality"""
    print("=== MEDICAL RECORD CHUNKING DEMONSTRATION ===\n")
    
    # Initialize embedder with chunking
    embedder = MedicalRecordEmbedder(enable_chunking=True, max_chunk_size=200)
    
    # Process a few sample records to show chunking
    dataset_path = "d:/Chatbot_hospital/dataset"
    dataset_path_obj = Path(dataset_path)
    
    # Get first 3 MD files for demonstration
    md_files = list(dataset_path_obj.glob("*.md"))[:3]
    
    print(f"Processing {len(md_files)} sample files to demonstrate chunking:")
    for file_path in md_files:
        print(f"  - {file_path.name}")
    print()
    
    # Parse the records
    records = []
    for file_path in md_files:
        record = embedder.parse_medical_record(str(file_path))
        if record:
            records.append(record)
            print(f"✓ Parsed: {file_path.name}")
    
    print(f"\nSuccessfully parsed {len(records)} records\n")
    
    # Demonstrate chunking for each record
    total_chunks = 0
    for i, record in enumerate(records):
        print(f"=== RECORD {i+1}: {Path(record.file_path).name} ===")
        print(f"Patient: {record.patient_id}")
        print(f"Type: {record.record_type}")
        print(f"Diagnosis: {record.primary_diagnosis}")
        print(f"Department: {record.department}")
        print()
        
        # Create chunks for this record
        chunks = embedder.chunk_medical_record(record)
        total_chunks += len(chunks)
        
        print(f"Generated {len(chunks)} chunks:")
        for chunk in chunks:
            print(f"  {chunk.chunk_index + 1}. Type: {chunk.chunk_type}")
            print(f"     ID: {chunk.chunk_id}")
            print(f"     Content preview: {chunk.content[:100]}...")
            print(f"     Metadata: {chunk.metadata}")
            print()
        
        print("-" * 60)
        print()
    
    print(f"=== CHUNKING SUMMARY ===")
    print(f"Total records processed: {len(records)}")
    print(f"Total chunks generated: {total_chunks}")
    print(f"Average chunks per record: {total_chunks / len(records):.1f}")
    
    # Analyze chunk types
    chunk_types = {}
    for record in records:
        chunks = embedder.chunk_medical_record(record)
        for chunk in chunks:
            chunk_type = chunk.chunk_type
            if chunk_type not in chunk_types:
                chunk_types[chunk_type] = 0
            chunk_types[chunk_type] += 1
    
    print("\nChunk type distribution:")
    for chunk_type, count in sorted(chunk_types.items()):
        print(f"  - {chunk_type}: {count} chunks")
    
    return records, total_chunks

def compare_search_results():
    """Compare search results between chunked and non-chunked embeddings"""
    print("\n=== SEARCH COMPARISON: CHUNKED vs NON-CHUNKED ===\n")
    
    # Process some records
    dataset_path = "d:/Chatbot_hospital/dataset"
    
    # Create two embedders: one with chunking, one without
    embedder_chunked = MedicalRecordEmbedder(enable_chunking=True, max_chunk_size=200)
    embedder_full = MedicalRecordEmbedder(enable_chunking=False)
    
    # Process a subset of records
    records = embedder_chunked.process_dataset(dataset_path)
    if len(records) > 10:  # Limit for demo
        records = records[:10]
    
    print(f"Processed {len(records)} records for comparison")
    
    # Create embeddings with both approaches
    print("\nCreating chunked embeddings...")
    chunked_embeddings = embedder_chunked.create_embeddings(records)
    
    print("Creating full record embeddings...")
    full_embeddings = embedder_full.create_embeddings(records)
    
    print(f"\nComparison:")
    print(f"  - Chunked approach: {len(chunked_embeddings)} embeddings")
    print(f"  - Full record approach: {len(full_embeddings)} embeddings")
    print(f"  - Granularity improvement: {len(chunked_embeddings) / len(full_embeddings):.1f}x more granular")
    
    # Show the benefits of chunking
    print(f"\n=== BENEFITS OF CHUNKING ===")
    print("1. GRANULAR SEARCH:")
    print("   - Each medical aspect (diagnosis, medications, procedures) gets its own embedding")
    print("   - Better matching for specific medical queries")
    print("   - More precise search results")
    print()
    
    print("2. IMPROVED ACCURACY:")
    print("   - Focused chunks avoid dilution of medical concepts")
    print("   - Specific medical terminology is better preserved")
    print("   - Contextual relationships are maintained within chunks")
    print()
    
    print("3. BETTER TRACEABILITY:")
    print("   - Each chunk maintains link to parent record and file")
    print("   - Can trace back to exact section of medical record")
    print("   - Preserves all file-level information")
    print()
    
    print("4. ENHANCED SEARCH CAPABILITIES:")
    print("   - Can search for specific medical aspects")
    print("   - Better performance on targeted queries")
    print("   - More relevant results for clinical questions")
    
    return chunked_embeddings, full_embeddings

def demonstrate_search_scenarios():
    """Demonstrate different search scenarios with chunked embeddings"""
    print("\n=== SEARCH SCENARIOS WITH CHUNKED EMBEDDINGS ===\n")
    
    # Example search scenarios that benefit from chunking
    scenarios = [
        {
            "name": "Diagnosis-focused search",
            "query": "patient with cardiac arrhythmia",
            "expected_chunks": ["diagnosis", "clinical_notes"],
            "description": "Should match diagnosis and clinical examination chunks"
        },
        {
            "name": "Medication search",
            "query": "patient taking beta blockers",
            "expected_chunks": ["medications"],
            "description": "Should primarily match medication-focused chunks"
        },
        {
            "name": "Procedure search",
            "query": "coronary angiography procedure",
            "expected_chunks": ["procedures", "clinical_notes"],
            "description": "Should match procedure and related clinical notes"
        },
        {
            "name": "Administrative search",
            "query": "emergency department admission",
            "expected_chunks": ["header"],
            "description": "Should match administrative/header information"
        },
        {
            "name": "Clinical examination search",
            "query": "physical examination findings",
            "expected_chunks": ["clinical_notes"],
            "description": "Should match clinical examination chunks"
        }
    ]
    
    print("Search scenarios that benefit from chunking:")
    for i, scenario in enumerate(scenarios, 1):
        print(f"{i}. {scenario['name']}")
        print(f"   Query: '{scenario['query']}'")
        print(f"   Expected chunk types: {', '.join(scenario['expected_chunks'])}")
        print(f"   Description: {scenario['description']}")
        print()
    
    print("These scenarios demonstrate how chunking enables more precise")
    print("and relevant search results by matching specific medical aspects.")

def main():
    """Main demonstration function"""
    try:
        # Demonstrate chunking
        records, total_chunks = demonstrate_chunking()
        
        # Compare approaches
        chunked_embeddings, full_embeddings = compare_search_results()
        
        # Show search scenarios
        demonstrate_search_scenarios()
        
        print(f"\n=== SUMMARY ===")
        print(f"✓ Successfully demonstrated medical record chunking")
        print(f"✓ Generated {total_chunks} chunks from {len(records)} records")
        print(f"✓ Showed {len(chunked_embeddings) / len(full_embeddings):.1f}x granularity improvement")
        print(f"✓ Maintained full traceability to original files")
        print(f"✓ Enhanced search capabilities for medical content")
        
    except Exception as e:
        logger.error(f"Error in demonstration: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
