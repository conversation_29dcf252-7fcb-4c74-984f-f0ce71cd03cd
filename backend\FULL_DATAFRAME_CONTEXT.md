# Full Dataframe Context Feature

## Overview

This feature allows the AI model to access the complete executed dataframe data instead of just sample data (previously limited to 5 rows). This enables more accurate chart generation, better data analysis, and improved context understanding.

## Configuration

The feature is controlled by three new configuration options in `config.py`:

### Environment Variables

```bash
# Enable/disable full dataframe context (default: true)
USE_FULL_DATAFRAME_CONTEXT=true

# Maximum number of rows to include in model context (default: 1000)
MAX_CONTEXT_ROWS=1000

# Maximum number of columns to include in model context (default: 50)
MAX_CONTEXT_COLUMNS=50
```

### Configuration Class Properties

- `USE_FULL_DATAFRAME_CONTEXT`: Boolean flag to enable/disable full dataframe context
- `MAX_CONTEXT_ROWS`: Maximum number of rows to include (prevents token overflow)
- `MAX_CONTEXT_COLUMNS`: Maximum number of columns to include (prevents token overflow)

## What Changed

### 1. Configuration (`config.py`)
- Added three new configuration options for controlling dataframe context behavior

### 2. Dataframe Serialization (`app/core/vanna_custom.py`)
- Modified `_serialize_dataframe_for_json()` to optionally include full dataframe
- Now respects `USE_FULL_DATAFRAME_CONTEXT` and `MAX_CONTEXT_ROWS` settings

### 3. Chat History Context (`app/core/vanna_custom.py`)
- Updated `add_dataframe_to_chat_history()` to include full dataframe data
- Provides clear indication of whether full data or sample data is being used
- Respects column limits to prevent token overflow

### 4. Chart Generation (`app/core/vanna_custom.py`)
- Enhanced `should_generate_chart_with_context()` to include actual dataframe data
- LLM now has access to real data values for more accurate chart generation
- Updated prompts to encourage use of actual data values when appropriate

## Benefits

### Before (Sample Data Only)
```
Sample data:
| doctor_name   |   patient_count | specialty   |
|:--------------|----------------:|:------------|
| Dr. Smith     |              15 | Cardiology  |
| Dr. Johnson   |              12 | Neurology   |
| Dr. Williams  |               8 | Pediatrics  |
| Dr. Brown     |               6 | Orthopedics |
| Dr. Davis     |               4 | Dermatology |
```

### After (Full Data)
```
Full data:
| doctor_name   |   patient_count | specialty   |
|:--------------|----------------:|:------------|
| Dr. Smith     |              15 | Cardiology  |
| Dr. Johnson   |              12 | Neurology   |
| Dr. Williams  |               8 | Pediatrics  |
| Dr. Brown     |               6 | Orthopedics |
| Dr. Davis     |               4 | Dermatology |
| Dr. Miller    |               3 | Psychiatry  |
| Dr. Wilson    |               2 | Radiology   |
```

## Chart Generation Improvements

The LLM now has access to complete data and can generate charts using either:

1. **DataFrame approach**: `fig = px.bar(df, x='doctor_name', y='patient_count')`
2. **Direct data approach**: `fig = px.bar(x=['Dr. Smith', 'Dr. Johnson', ...], y=[15, 12, ...])`

This results in more accurate visualizations that represent the complete dataset.

## Safety Features

- **Row Limits**: Configurable maximum rows to prevent token overflow
- **Column Limits**: Configurable maximum columns to prevent token overflow
- **Backward Compatibility**: Can be disabled to revert to sample-only behavior
- **Graceful Fallback**: Automatically handles import errors and missing configurations

## Testing

Run the test script to verify the feature is working:

```bash
cd backend
python test_full_dataframe_context.py
```

Expected output should show:
- ✅ SUCCESS: Full dataframe context is being used (X rows)
- Full dataframe data in the context message
- Proper configuration values loaded

## Usage Examples

### Enable Full Context (Default)
```bash
export USE_FULL_DATAFRAME_CONTEXT=true
export MAX_CONTEXT_ROWS=1000
export MAX_CONTEXT_COLUMNS=50
```

### Disable Full Context (Revert to Sample)
```bash
export USE_FULL_DATAFRAME_CONTEXT=false
```

### Custom Limits
```bash
export USE_FULL_DATAFRAME_CONTEXT=true
export MAX_CONTEXT_ROWS=500
export MAX_CONTEXT_COLUMNS=20
```

## Impact on Performance

- **Memory**: Slightly higher memory usage due to larger context
- **Tokens**: More tokens consumed per request (controlled by limits)
- **Accuracy**: Significantly improved chart generation and data analysis
- **Response Quality**: Better understanding of complete dataset patterns

The benefits of improved accuracy typically outweigh the modest increase in resource usage.
