#!/usr/bin/env python3
"""
Simple test to verify the system prompt contains the correct identity instructions.
"""

import sys
import os

# Add the backend directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.core.vanna_custom import MyVanna
from config import Config

def test_system_prompt():
    """Test that the system prompt contains the correct identity instructions"""
    print("🧪 Testing system prompt for identity instructions...")
    
    # Create a minimal config
    config = Config()
    
    # Create MyVanna instance with minimal setup (skip ChromaDB)
    try:
        # We'll test the get_sql_prompt method directly
        vn = MyVanna.__new__(MyVanna)  # Create instance without calling __init__
        vn.config = config.vanna_config
        vn.dialect = 'PostgreSQL'
        vn.static_documentation = ""
        vn.max_tokens = 14000
        
        # Test the get_sql_prompt method
        prompt = vn.get_sql_prompt(
            initial_prompt_text=None,
            question="What's your name?",
            question_sql_list=[],
            ddl_list=[],
            doc_list=[],
            chat_history=[]
        )
        
        # Extract system message content
        if isinstance(prompt, list) and len(prompt) > 0:
            system_message = prompt[0]
            if isinstance(system_message, dict) and 'content' in system_message:
                system_content = system_message['content']
                
                print("📝 System prompt generated successfully!")
                print("\n🔍 Checking for identity instructions...")
                
                # Check for the identity instruction
                identity_checks = [
                    "I'm your hospital assistant. How can I help?" in system_content,
                    "IDENTITY QUESTIONS" in system_content,
                    "What's your name?" in system_content,
                    "do not mention any other model names" in system_content
                ]
                
                print(f"✅ Contains exact response: {identity_checks[0]}")
                print(f"✅ Contains identity section: {identity_checks[1]}")
                print(f"✅ Contains example question: {identity_checks[2]}")
                print(f"✅ Contains restriction: {identity_checks[3]}")
                
                if all(identity_checks):
                    print("\n🎉 SUCCESS: System prompt contains all required identity instructions!")
                    
                    # Show relevant parts of the prompt
                    print("\n📋 Relevant prompt sections:")
                    lines = system_content.split('\n')
                    for i, line in enumerate(lines):
                        if any(keyword in line.lower() for keyword in ['identity', 'name', 'hospital assistant']):
                            print(f"  Line {i+1}: {line.strip()}")
                    
                    return True
                else:
                    print("\n❌ FAILED: System prompt missing some identity instructions!")
                    print("\n📋 Full system prompt:")
                    print(system_content)
                    return False
            else:
                print("❌ System message format unexpected")
                return False
        else:
            print("❌ Prompt format unexpected")
            return False
            
    except Exception as e:
        print(f"❌ Error testing system prompt: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_response_guidelines():
    """Test that response guidelines contain identity instructions"""
    print("\n🧪 Testing response guidelines section...")
    
    try:
        vn = MyVanna.__new__(MyVanna)
        vn.config = {'dialect': 'PostgreSQL'}
        vn.dialect = 'PostgreSQL'
        vn.static_documentation = ""
        vn.max_tokens = 14000
        
        # Get the prompt to check response guidelines
        prompt = vn.get_sql_prompt(
            initial_prompt_text=None,
            question="Test question",
            question_sql_list=[],
            ddl_list=[],
            doc_list=[],
            chat_history=[]
        )
        
        if isinstance(prompt, list) and len(prompt) > 0:
            system_content = prompt[0]['content']
            
            # Look for response guidelines section
            if "===Response Guidelines" in system_content:
                guidelines_section = system_content.split("===Response Guidelines")[1]
                
                print("📝 Response guidelines found!")
                
                # Check for identity guideline
                identity_guideline_checks = [
                    "0.1. IDENTITY QUESTIONS" in guidelines_section,
                    "What's your name?" in guidelines_section,
                    "I'm your hospital assistant. How can I help?" in guidelines_section,
                    "do not mention any other model names" in guidelines_section
                ]
                
                print(f"✅ Has identity guideline section: {identity_guideline_checks[0]}")
                print(f"✅ Has example question: {identity_guideline_checks[1]}")
                print(f"✅ Has exact response: {identity_guideline_checks[2]}")
                print(f"✅ Has restriction clause: {identity_guideline_checks[3]}")
                
                if all(identity_guideline_checks):
                    print("\n🎉 SUCCESS: Response guidelines contain identity instructions!")
                    return True
                else:
                    print("\n❌ FAILED: Response guidelines missing identity instructions!")
                    print("\n📋 Guidelines section:")
                    print(guidelines_section[:1000] + "..." if len(guidelines_section) > 1000 else guidelines_section)
                    return False
            else:
                print("❌ Response guidelines section not found")
                return False
        else:
            print("❌ Could not extract system content")
            return False
            
    except Exception as e:
        print(f"❌ Error testing response guidelines: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 Starting prompt fix verification...")
    
    # Test system prompt
    prompt_result = test_system_prompt()
    
    # Test response guidelines
    guidelines_result = test_response_guidelines()
    
    print("\n" + "="*60)
    print("📊 TEST RESULTS SUMMARY")
    print("="*60)
    print(f"System Prompt Test: {'✅ PASSED' if prompt_result else '❌ FAILED'}")
    print(f"Response Guidelines Test: {'✅ PASSED' if guidelines_result else '❌ FAILED'}")
    
    if prompt_result and guidelines_result:
        print("\n🎉 ALL TESTS PASSED! TC004 fix is properly implemented in the prompt!")
        sys.exit(0)
    else:
        print("\n❌ SOME TESTS FAILED! TC004 fix needs verification.")
        sys.exit(1)
