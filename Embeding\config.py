"""
Configuration file for medical record embedder
"""

# Qdrant Configuration
QDRANT_HOST = "localhost"
QDRANT_PORT = 6333
COLLECTION_NAME = "medical_records_v2"

# Embedding Configuration
EMBEDDING_MODEL = "sentence-transformers/all-MiniLM-L6-v2"
# Alternative models for French medical text:
# EMBEDDING_MODEL = "sentence-transformers/distiluse-base-multilingual-cased"
# EMBEDDING_MODEL = "sentence-transformers/paraphrase-multilingual-MiniLM-L12-v2"

# Processing Configuration
SIMILARITY_THRESHOLD = 0.9  # For deduplication
BATCH_SIZE = 100
MIN_CONTENT_LENGTH = 50

# Dataset Configuration
DATASET_PATH = "d:/Chatbot_hospital/dataset"
PROCESSED_DATA_PATH = "d:/Chatbot_hospital/processed_data"

# Medical specialties mapping
MEDICAL_SPECIALTIES = {
    'cardiology': {
        'keywords': ['cardio', 'coeur', 'arythmie', 'infarctus', 'ECG', 'troponine', 'coronarien', 'SCA', 'NSTEMI'],
        'weight': 1.5
    },
    'psychiatry': {
        'keywords': ['psy', 'depression', 'anxiete', 'suicidaire', 'intoxication', 'IMV', 'tentative', 'suicide'],
        'weight': 1.3
    },
    'neurology': {
        'keywords': ['neuro', 'AVC', 'confusion', 'deficit', 'Glasgow', 'neurologique', 'cerebral'],
        'weight': 1.4
    },
    'gastroenterology': {
        'keywords': ['gastro', 'abdomen', 'digestif', 'hémorragie', 'appendicite', 'FID', 'nausée'],
        'weight': 1.2
    },
    'endocrinology': {
        'keywords': ['diabete', 'glycemie', 'insuline', 'acidocetose', 'thyroide', 'hormone'],
        'weight': 1.2
    },
    'emergency': {
        'keywords': ['urgence', 'SAMU', 'pompiers', 'accident', 'trauma', 'IOA'],
        'weight': 1.1
    }
}

# Template patterns to remove (common administrative content)
TEMPLATE_PATTERNS = [
    r"Unité d'Hospitalisation.*?Secrétariat.*?\d+",
    r"Date d'entrée.*?Date de sortie.*?N°.*?C-\d+",
    r"Mode d'arrivée.*?(?=\n)",
    r"Médecin traitant.*?(?=\n)",
    r"Personne à prévenir.*?(?=\n)",
    r"Prise en charge social.*?(?=\n)",
    r"Accueil IOA.*?(?=\n)",
    r"T°\s*:\s*\d+,\d+\s*°C.*?Glasgow\s*:\s*\d+",
    r"Centre Hospitalier.*?(?=\n)",
    r"IDEPHI\s*\d+.*?(?=\n)",
    r"NIP\s*:\s*\d+",
    r"Edition du.*?(?=\n)",
    r"E-mail\s*:.*?(?=\n)",
    r"Documents donnés au Patient",
    r"Sortie du service",
    r"Décision d'orientation",
    r"avec passage.*?(?=\n|$)"
]
