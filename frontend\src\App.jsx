import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { ThemeProvider } from './contexts/ThemeContext';
import { AuthProvider } from './contexts/AuthContext';
import { ChatProvider } from './contexts/ChatContext';
import Layout from './components/Layout/Layout';
import Login from './pages/Login';
import Dashboard from './pages/Dashboard';
import Chatbot from './pages/Chatbot';
import PatientSearch from './pages/PatientSearch';
import ConsultationSearch from './pages/ConsultationSearch';
import DoctorSearch from './pages/DoctorSearch';

import Settings from './pages/Settings';
import ProtectedRoute from './components/Auth/ProtectedRoute';
import './styles/chat-history.css';

function App() {
  return (
    <ThemeProvider>
      <AuthProvider>
        <ChatProvider>
          <Router>
            <div className="App">
              <Routes>
              {/* Public routes */}
              <Route path="/login" element={<Login />} />
              
              {/* Protected routes */}
              <Route path="/" element={
                <ProtectedRoute>
                  <Layout>
                    <Navigate to="/chatbot" replace />
                  </Layout>
                </ProtectedRoute>
              } />
              
              <Route path="/dashboard" element={
                <ProtectedRoute>
                  <Layout>
                    <Dashboard />
                  </Layout>
                </ProtectedRoute>
              } />
              
              <Route path="/chatbot" element={
                <ProtectedRoute>
                  <Layout>
                    <Chatbot />
                  </Layout>
                </ProtectedRoute>
              } />
              
              <Route path="/patient-search" element={
                <ProtectedRoute>
                  <Layout>
                    <PatientSearch />
                  </Layout>
                </ProtectedRoute>
              } />

              <Route path="/consultation-search" element={
                <ProtectedRoute>
                  <Layout>
                    <ConsultationSearch />
                  </Layout>
                </ProtectedRoute>
              } />

              <Route path="/doctor-search" element={
                <ProtectedRoute>
                  <Layout>
                    <DoctorSearch />
                  </Layout>
                </ProtectedRoute>
              } />

              <Route path="/settings" element={
                <ProtectedRoute>
                  <Layout>
                    <Settings />
                  </Layout>
                </ProtectedRoute>
              } />
              
              {/* Catch all route */}
              <Route path="*" element={<Navigate to="/chatbot" replace />} />
            </Routes>
          </div>
        </Router>
        </ChatProvider>
      </AuthProvider>
    </ThemeProvider>
  );
}

export default App;
