import React, { useState } from 'react';
import LoadingSpinner from '../components/UI/LoadingSpinner';
import PatientDetailsModal from '../components/Modals/PatientDetailsModal';

const PatientSearch = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [searchType, setSearchType] = useState('name');
  const [results, setResults] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [hasSearched, setHasSearched] = useState(false);
  const [selectedPatientId, setSelectedPatientId] = useState(null);
  const [showPatientDetails, setShowPatientDetails] = useState(false);

  const searchTypes = [
    { value: 'name', label: 'Patient Name', icon: 'fas fa-user' },
    { value: 'id', label: 'Patient ID', icon: 'fas fa-id-card' },
    { value: 'phone', label: 'Phone Number', icon: 'fas fa-phone' },
    { value: 'email', label: 'Email', icon: 'fas fa-envelope' },
    { value: 'diagnosis', label: 'Diagnosis', icon: 'fas fa-stethoscope' }
  ];

  const handleSearch = async (e) => {
    e.preventDefault();
    
    if (!searchQuery.trim()) {
      setError('Please enter a search term');
      return;
    }

    setLoading(true);
    setError('');
    setHasSearched(true);

    try {
      const response = await fetch('/api/v1/search/patients', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          query: searchQuery.trim(),
          type: searchType
        })
      });

      const data = await response.json();

      if (response.ok) {
        setResults(data.results || []);
      } else {
        setError(data.error || 'Search failed. Please try again.');
        setResults([]);
      }
    } catch (error) {
      console.error('Search error:', error);
      setError('Network error. Please check your connection and try again.');
      setResults([]);
    } finally {
      setLoading(false);
    }
  };

  const handleClear = () => {
    setSearchQuery('');
    setResults([]);
    setError('');
    setHasSearched(false);
  };

  const handlePatientClick = (patient) => {
    setSelectedPatientId(patient.patient_id);
    setShowPatientDetails(true);
  };

  const handleClosePatientDetails = () => {
    setShowPatientDetails(false);
    setSelectedPatientId(null);
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  return (
    <div className="p-8">
      {/* Header */}
      <h2 className="text-2xl font-semibold text-gray-900 dark:text-gray-100 border-b border-gray-200 dark:border-gray-700 pb-4 mb-8">
        <i className="fas fa-search mr-3"></i>
        Patient Search
      </h2>

      {/* Search Form */}
      <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6 mb-8">
        <form onSubmit={handleSearch} className="space-y-6">
          {/* Search Type Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
              Search By:
            </label>
            <div className="grid grid-cols-2 md:grid-cols-5 gap-3">
              {searchTypes.map((type) => (
                <label
                  key={type.value}
                  className={`
                    flex items-center p-3 border rounded-lg cursor-pointer transition-all
                    ${searchType === type.value
                      ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300'
                      : 'border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500'
                    }
                  `}
                >
                  <input
                    type="radio"
                    name="searchType"
                    value={type.value}
                    checked={searchType === type.value}
                    onChange={(e) => setSearchType(e.target.value)}
                    className="sr-only"
                  />
                  <i className={`${type.icon} mr-2`}></i>
                  <span className="text-sm font-medium">{type.label}</span>
                </label>
              ))}
            </div>
          </div>

          {/* Search Input */}
          <div className="flex space-x-4">
            <div className="flex-1">
              <input
                type="text"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                placeholder={`Enter ${searchTypes.find(t => t.value === searchType)?.label.toLowerCase()}...`}
                className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                disabled={loading}
              />
            </div>
            
            <button
              type="submit"
              disabled={loading || !searchQuery.trim()}
              className="px-6 py-3 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white font-medium rounded-lg transition-colors flex items-center"
            >
              {loading ? (
                <i className="fas fa-spinner animate-spin mr-2"></i>
              ) : (
                <i className="fas fa-search mr-2"></i>
              )}
              {loading ? 'Searching...' : 'Search'}
            </button>

            {hasSearched && (
              <button
                type="button"
                onClick={handleClear}
                className="px-4 py-3 bg-gray-500 hover:bg-gray-600 text-white font-medium rounded-lg transition-colors"
              >
                <i className="fas fa-times mr-2"></i>
                Clear
              </button>
            )}
          </div>
        </form>
      </div>

      {/* Error Message */}
      {error && (
        <div className="mb-6 p-4 bg-red-100 dark:bg-red-900 border border-red-300 dark:border-red-700 text-red-700 dark:text-red-300 rounded-lg">
          <i className="fas fa-exclamation-triangle mr-2"></i>
          {error}
        </div>
      )}

      {/* Loading State */}
      {loading && (
        <div className="flex justify-center py-12">
          <LoadingSpinner size="lg" text="Searching patients..." />
        </div>
      )}

      {/* Search Results */}
      {!loading && hasSearched && (
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
          <div className="p-6 border-b border-gray-200 dark:border-gray-700">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
              Search Results
              {results.length > 0 && (
                <span className="ml-2 text-sm font-normal text-gray-500 dark:text-gray-400">
                  ({results.length} patient{results.length !== 1 ? 's' : ''} found)
                </span>
              )}
            </h3>
          </div>
          
          <div className="p-6">
            {results.length > 0 ? (
              <div className="overflow-x-auto">
                <table className="min-w-full bg-white dark:bg-gray-800 rounded-lg overflow-hidden">
                  <thead className="bg-gray-50 dark:bg-gray-900">
                    <tr>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        Patient ID
                      </th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        Full Name
                      </th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        Birth Date
                      </th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        Gender
                      </th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                        Contact
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                    {results.map((patient, index) => (
                      <tr
                        key={index}
                        className="hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer transition-colors"
                        onClick={() => handlePatientClick(patient)}
                        title="Click to view patient details"
                      >
                        <td className="px-4 py-3 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-gray-100">
                          {patient.patient_id || 'N/A'}
                        </td>
                        <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                          <div>
                            <div className="font-medium">{patient.first_name} {patient.last_name}</div>
                          </div>
                        </td>
                        <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                          {formatDate(patient.date_of_birth)}
                        </td>
                        <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                            patient.gender === 'M'
                              ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
                              : patient.gender === 'F'
                              ? 'bg-pink-100 text-pink-800 dark:bg-pink-900 dark:text-pink-200'
                              : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200'
                          }`}>
                            {patient.gender === 'M' ? 'Male' : patient.gender === 'F' ? 'Female' : 'N/A'}
                          </span>
                        </td>
                        <td className="px-4 py-3 text-sm text-gray-900 dark:text-gray-100">
                          <div>
                            {patient.phone && (
                              <div className="text-xs">
                                <i className="fas fa-phone mr-1"></i>
                                {patient.phone}
                              </div>
                            )}
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            ) : (
              <div className="text-center py-12 text-gray-500 dark:text-gray-400">
                <i className="fas fa-user-slash text-4xl mb-4"></i>
                <p className="text-lg font-medium mb-2">No patients found</p>
                <p className="text-sm">Try adjusting your search criteria or search term.</p>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Patient Details Modal */}
      <PatientDetailsModal
        isOpen={showPatientDetails}
        onClose={handleClosePatientDetails}
        patientId={selectedPatientId}
      />
    </div>
  );
};

export default PatientSearch;
