"""
Advanced Medical Record Embedding System for Hospital Chatbot

This system preprocesses medical records to extract meaningful content,
reduce redundancy, and improve embedding quality for vector search.
"""

import os
import re
import json
import hashlib
from typing import Dict, List, Tuple, Optional, Set
from pathlib import Path
import numpy as np
from sentence_transformers import SentenceTransformer
from qdrant_client import QdrantClient
from qdrant_client.models import Distance, VectorParams, PointStruct
from dataclasses import dataclass, asdict
from datetime import datetime
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class MedicalRecord:
    """Structure for parsed medical record"""
    patient_id: str
    record_type: str
    admission_date: str
    discharge_date: str
    primary_diagnosis: str
    secondary_diagnoses: List[str]
    clinical_notes: List[str]
    medications: List[str]
    allergies: List[str]
    vital_signs: Dict[str, str]
    procedures: List[str]
    physician: str
    department: str
    file_path: str
    content_hash: str
    specialized_content: str  # Non-template content

@dataclass
class MedicalRecordChunk:
    """Structure for medical record chunks"""
    chunk_id: str
    parent_record: MedicalRecord
    chunk_type: str  # 'header', 'diagnosis', 'clinical_notes', 'medications', 'procedures', 'summary'
    content: str
    chunk_index: int
    metadata: Dict[str, str]
    
class MedicalRecordEmbedder:
    """
    Advanced medical record embedder with deduplication and content extraction
    """
    
    def __init__(self, model_name: str = "sentence-transformers/all-MiniLM-L6-v2", 
                 enable_chunking: bool = True, max_chunk_size: int = 300):
        """
        Initialize the embedder with specified model
        
        Args:
            model_name: Name of the sentence transformer model
            enable_chunking: Whether to enable section chunking for granular embeddings
            max_chunk_size: Maximum size for each chunk (in words)
        """
        self.model = SentenceTransformer(model_name)
        self.qdrant_client = None
        self.collection_name = "medical_records"
        self.enable_chunking = enable_chunking
        self.max_chunk_size = max_chunk_size
        
        # Common template patterns to exclude from embedding
        self.template_patterns = [
            r"Unité d'Hospitalisation.*Secrétariat.*",
            r"Date d'entrée.*Date de sortie.*",
            r"Mode d'arrivée.*",
            r"Médecin traitant.*",
            r"Personne à prévenir.*",
            r"Prise en charge social.*",
            r"Accueil IOA.*",
            r"T°.*°C.*TA.*mm.*Hg.*",
            r"Glasgow.*15.*",
            r"Centre Hospitalier.*",
            r"IDEPHI.*",
            r"NIP.*",
            r"Edition du.*",
            r"E-mail.*",
            r"Documents donnés au Patient",
            r"Sortie du service",
            r"Décision d'orientation"
        ]
        
        # Medical terminology for content classification
        self.medical_keywords = {
            'cardiology': ['cardio', 'coeur', 'arythmie', 'infarctus', 'ECG', 'troponine', 'coronarien'],
            'psychiatry': ['psy', 'depression', 'anxiete', 'suicidaire', 'intoxication', 'IMV'],
            'neurology': ['neuro', 'AVC', 'confusion', 'deficit', 'Glasgow', 'neurologique'],
            'gastroenterology': ['gastro', 'abdomen', 'digestif', 'hémorragie', 'appendicite'],
            'endocrinology': ['diabete', 'glycemie', 'insuline', 'acidocetose', 'thyroide'],
            'emergency': ['urgence', 'SAMU', 'pompiers', 'accident', 'trauma']
        }
        
    def setup_qdrant(self, host: str = "localhost", port: int = 6333):
        """Setup Qdrant client and collection"""
        self.qdrant_client = QdrantClient(host=host, port=port)
        
        # Create collection if it doesn't exist
        try:
            self.qdrant_client.create_collection(
                collection_name=self.collection_name,
                vectors_config=VectorParams(
                    size=self.model.get_sentence_embedding_dimension(),
                    distance=Distance.COSINE
                )
            )
            logger.info(f"Created collection: {self.collection_name}")
        except Exception as e:
            logger.info(f"Collection already exists or error: {e}")
    
    def extract_medical_content(self, text: str) -> str:
        """
        Extract meaningful medical content, removing template boilerplate
        
        Args:
            text: Raw medical record text
            
        Returns:
            Cleaned medical content
        """
        # Remove template patterns
        cleaned_text = text
        for pattern in self.template_patterns:
            cleaned_text = re.sub(pattern, '', cleaned_text, flags=re.IGNORECASE | re.MULTILINE)
        
        # Extract meaningful sections
        meaningful_sections = []
        
        # Extract clinical examination notes
        exam_matches = re.findall(r'Examen clinique.*?(?=\n\n|\nFICHES|$)', cleaned_text, re.DOTALL | re.IGNORECASE)
        meaningful_sections.extend(exam_matches)
        
        # Extract medical history
        history_matches = re.findall(r'Histoire de la maladie.*?(?=\n\n|\nExamen|$)', cleaned_text, re.DOTALL | re.IGNORECASE)
        meaningful_sections.extend(history_matches)
        
        # Extract diagnosis and conclusions
        diagnosis_matches = re.findall(r'(?:Diagnostic|Conclusion).*?(?=\n\n|\nDocuments|$)', cleaned_text, re.DOTALL | re.IGNORECASE)
        meaningful_sections.extend(diagnosis_matches)
        
        # Extract medication information
        medication_matches = re.findall(r'(?:Traitement|Médicament|mg|UI).*?(?=\n\n|$)', cleaned_text, re.DOTALL | re.IGNORECASE)
        meaningful_sections.extend(medication_matches)
        
        # Extract psychiatric evaluations
        psy_matches = re.findall(r'PSY.*?(?=\n\n|\nConclusion|$)', cleaned_text, re.DOTALL | re.IGNORECASE)
        meaningful_sections.extend(psy_matches)
        
        # Join and clean
        result = ' '.join(meaningful_sections)
        result = re.sub(r'\s+', ' ', result).strip()
        
        return result if result else cleaned_text[:500]  # Fallback to first 500 chars
    
    def parse_medical_record(self, file_path: str) -> Optional[MedicalRecord]:
        """
        Parse a medical record file into structured format
        
        Args:
            file_path: Path to the medical record file
            
        Returns:
            Parsed MedicalRecord object or None if parsing fails
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Extract basic information
            patient_name = self._extract_field(content, r'(\w+(?:-\w+)*\s+\w+)\s*-\s*Né')
            admission_date = self._extract_field(content, r'Date d\'entrée\s*:\s*([^N]+)')
            discharge_date = self._extract_field(content, r'Date de sortie\s*:\s*([^N]+)')
            physician = self._extract_field(content, r'Dr\s+([^E]+)')
            department = self._extract_field(content, r'Unité\s+([^S]+)')
            
            # Extract medical content
            specialized_content = self.extract_medical_content(content)
            
            # Generate content hash for deduplication
            content_hash = hashlib.md5(specialized_content.encode()).hexdigest()
            
            # Classify record type based on content
            record_type = self._classify_record_type(specialized_content)
            
            # Extract structured medical information
            diagnoses = self._extract_diagnoses(content)
            medications = self._extract_medications(content)
            allergies = self._extract_allergies(content)
            
            return MedicalRecord(
                patient_id=patient_name or "Unknown",
                record_type=record_type,
                admission_date=admission_date or "",
                discharge_date=discharge_date or "",
                primary_diagnosis=diagnoses[0] if diagnoses else "Not specified",
                secondary_diagnoses=diagnoses[1:] if len(diagnoses) > 1 else [],
                clinical_notes=self._extract_clinical_notes(content),
                medications=medications,
                allergies=allergies,
                vital_signs=self._extract_vital_signs(content),
                procedures=self._extract_procedures(content),
                physician=physician or "Unknown",
                department=department or "Unknown",
                file_path=file_path,
                content_hash=content_hash,
                specialized_content=specialized_content
            )
            
        except Exception as e:
            logger.error(f"Error parsing {file_path}: {e}")
            return None
    
    def _extract_field(self, content: str, pattern: str) -> str:
        """Extract a field using regex pattern"""
        match = re.search(pattern, content, re.IGNORECASE)
        return match.group(1).strip() if match else ""
    
    def _classify_record_type(self, content: str) -> str:
        """Classify record type based on medical keywords"""
        content_lower = content.lower()
        scores = {}
        
        for specialty, keywords in self.medical_keywords.items():
            score = sum(1 for keyword in keywords if keyword in content_lower)
            if score > 0:
                scores[specialty] = score
        
        return max(scores, key=scores.get) if scores else "general"
    
    def _extract_diagnoses(self, content: str) -> List[str]:
        """Extract diagnoses from medical record"""
        diagnoses = []
        
        # Look for specific diagnosis patterns
        patterns = [
            r'Motif IOA\s*:\s*([^\n]+)',
            r'Diagnostic\s*:\s*([^\n]+)',
            r'Conclusion\s*:\s*([^\n]+)'
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            diagnoses.extend([m.strip() for m in matches if m.strip()])
        
        return list(set(diagnoses))  # Remove duplicates
    
    def _extract_medications(self, content: str) -> List[str]:
        """Extract medications from medical record"""
        medications = []
        
        # Look for medication patterns
        med_patterns = [
            r'([A-Z][a-z]+(?:ine|ol|ide|ium|ate))\s*\d+\s*(?:mg|UI|g)',
            r'(Ibuprofène|Paracétamol|Aspirine|Lexomil|Seresta|Tramadol|Xanax)',
            r'Traitement.*?:\s*([^\n]+)'
        ]
        
        for pattern in med_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            medications.extend([m.strip() for m in matches if m.strip()])
        
        return list(set(medications))
    
    def _extract_allergies(self, content: str) -> List[str]:
        """Extract allergies from medical record"""
        allergies = []
        
        # Look for allergy patterns
        allergy_section = re.search(r'Allergies\s*:?\s*([^\n]+)', content, re.IGNORECASE)
        if allergy_section:
            allergy_text = allergy_section.group(1).strip()
            if allergy_text.lower() not in ['aucune', 'aucun', 'none', '']:
                allergies.append(allergy_text)
        
        return allergies
    
    def _extract_clinical_notes(self, content: str) -> List[str]:
        """Extract clinical examination notes"""
        notes = []
        
        # Extract examination notes
        exam_pattern = r'Examen clinique du[^:]*:\s*([^\n]+(?:\n[^\n]+)*?)(?=\n\n|\nMode de vie|\nFICHES|$)'
        matches = re.findall(exam_pattern, content, re.DOTALL | re.IGNORECASE)
        
        for match in matches:
            cleaned = re.sub(r'\s+', ' ', match).strip()
            if len(cleaned) > 20:  # Only meaningful notes
                notes.append(cleaned)
        
        return notes
    
    def _extract_vital_signs(self, content: str) -> Dict[str, str]:
        """Extract vital signs from medical record"""
        vital_signs = {}
        
        # Temperature
        temp_match = re.search(r'T°\s*:\s*([^,]+)', content)
        if temp_match:
            vital_signs['temperature'] = temp_match.group(1).strip()
        
        # Blood pressure
        bp_match = re.search(r'TA[^:]*:\s*([^,]+)', content)
        if bp_match:
            vital_signs['blood_pressure'] = bp_match.group(1).strip()
        
        # Heart rate
        hr_match = re.search(r'Pouls\s*:\s*([^,]+)', content)
        if hr_match:
            vital_signs['heart_rate'] = hr_match.group(1).strip()
        
        return vital_signs
    
    def _extract_procedures(self, content: str) -> List[str]:
        """Extract medical procedures from record"""
        procedures = []
        
        # Look for procedure patterns
        proc_patterns = [
            r'(coronarographie|échographie|endoscopie|appendicectomie|ECG)',
            r'CAT\s*:\s*([^\n]+)'
        ]
        
        for pattern in proc_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            procedures.extend([m.strip() for m in matches if m.strip()])
        
        return list(set(procedures))
    
    def deduplicate_records(self, records: List[MedicalRecord]) -> List[MedicalRecord]:
        """
        Remove only exact duplicate records based on content hash
        Preserves near-duplicates to maintain file-specific information
        
        Args:
            records: List of parsed medical records
            
        Returns:
            Deduplicated list of records (only exact duplicates removed)
        """
        unique_records = []
        seen_hashes = set()
        duplicate_groups = {}
        
        for record in records:
            # Only remove exact duplicates (same content hash)
            if record.content_hash not in seen_hashes:
                unique_records.append(record)
                seen_hashes.add(record.content_hash)
                
                # Track which files have the same content for logging
                if record.content_hash not in duplicate_groups:
                    duplicate_groups[record.content_hash] = []
                duplicate_groups[record.content_hash].append(record.file_path)
            else:
                # Find the group this belongs to
                duplicate_groups[record.content_hash].append(record.file_path)
                logger.info(f"Skipping exact duplicate: {record.file_path} (same as {duplicate_groups[record.content_hash][0]})")
        
        # Log duplicate groups for reference
        exact_duplicate_groups = {k: v for k, v in duplicate_groups.items() if len(v) > 1}
        if exact_duplicate_groups:
            logger.info(f"Found {len(exact_duplicate_groups)} groups of exact duplicates:")
            for hash_key, files in exact_duplicate_groups.items():
                logger.info(f"  - Hash {hash_key[:8]}...: {files}")
        
        logger.info(f"Preserved {len(unique_records)} records (removed only exact duplicates)")
        return unique_records
    
    def _calculate_similarity(self, text1: str, text2: str) -> float:
        """Calculate similarity between two texts"""
        # Simple similarity based on common words
        words1 = set(text1.lower().split())
        words2 = set(text2.lower().split())
        
        if not words1 or not words2:
            return 0.0
        
        intersection = words1.intersection(words2)
        union = words1.union(words2)
        
        return len(intersection) / len(union) if union else 0.0
    
    def create_embeddings(self, records: List[MedicalRecord]) -> List[Tuple[MedicalRecord, np.ndarray]]:
        """
        Create embeddings for medical records (with optional chunking)
        
        Args:
            records: List of parsed medical records
            
        Returns:
            List of (record, embedding) tuples or (chunk, embedding) tuples if chunking enabled
        """
        if not self.enable_chunking:
            # Original behavior: single embedding per record
            return self._create_record_embeddings(records)
        else:
            # New behavior: chunk-based embeddings
            return self._create_chunk_embeddings(records)
    
    def _create_record_embeddings(self, records: List[MedicalRecord]) -> List[Tuple[MedicalRecord, np.ndarray]]:
        """Create single embedding per record (original behavior)"""
        embeddings = []
        
        for record in records:
            embedding_text = self._create_embedding_text(record)
            embedding = self.model.encode(embedding_text)
            embeddings.append((record, embedding))
            logger.info(f"Created embedding for: {record.file_path}")
        
        return embeddings
    
    def _create_chunk_embeddings(self, records: List[MedicalRecord]) -> List[Tuple[MedicalRecordChunk, np.ndarray]]:
        """Create embeddings for record chunks (new chunking behavior)"""
        embeddings = []
        
        for record in records:
            # Create chunks for this record
            chunks = self.chunk_medical_record(record)
            
            for chunk in chunks:
                embedding = self.model.encode(chunk.content)
                embeddings.append((chunk, embedding))
                logger.info(f"Created embedding for chunk: {chunk.chunk_id} ({chunk.chunk_type})")
        
        logger.info(f"Created {len(embeddings)} chunk embeddings from {len(records)} records")
        return embeddings
    
    def _create_embedding_text(self, record: MedicalRecord) -> str:
        """Create optimized text for embedding"""
        components = []
        
        # Add record type and specialty
        components.append(f"Type: {record.record_type}")
        components.append(f"Department: {record.department}")
        
        # Add primary diagnosis
        if record.primary_diagnosis:
            components.append(f"Primary diagnosis: {record.primary_diagnosis}")
        
        # Add secondary diagnoses
        if record.secondary_diagnoses:
            components.append(f"Secondary diagnoses: {', '.join(record.secondary_diagnoses)}")
        
        # Add medications
        if record.medications:
            components.append(f"Medications: {', '.join(record.medications)}")
        
        # Add allergies
        if record.allergies:
            components.append(f"Allergies: {', '.join(record.allergies)}")
        
        # Add clinical notes (most important)
        if record.clinical_notes:
            components.append(f"Clinical notes: {' '.join(record.clinical_notes)}")
        
        # Add specialized content
        if record.specialized_content:
            components.append(f"Medical content: {record.specialized_content}")
        
        return ' '.join(components)
    
    def store_in_qdrant(self, embeddings: List[Tuple]):
        """
        Store embeddings in Qdrant vector database
        
        Args:
            embeddings: List of (record/chunk, embedding) tuples
        """
        if not self.qdrant_client:
            raise ValueError("Qdrant client not initialized. Call setup_qdrant() first.")
        
        points = []
        for i, (item, embedding) in enumerate(embeddings):
            # Handle both MedicalRecord and MedicalRecordChunk
            if isinstance(item, MedicalRecordChunk):
                # For chunks, include parent record info and chunk-specific metadata
                payload = {
                    'chunk_id': item.chunk_id,
                    'chunk_type': item.chunk_type,
                    'chunk_index': item.chunk_index,
                    'content': item.content,
                    'metadata': item.metadata,
                    # Parent record information
                    'parent_file_path': item.parent_record.file_path,
                    'parent_patient_id': item.parent_record.patient_id,
                    'parent_record_type': item.parent_record.record_type,
                    'parent_primary_diagnosis': item.parent_record.primary_diagnosis,
                    'parent_department': item.parent_record.department,
                    'parent_physician': item.parent_record.physician,
                    'parent_content_hash': item.parent_record.content_hash,
                    'is_chunk': True
                }
            else:
                # For full records, use original structure
                payload = asdict(item)
                payload['is_chunk'] = False
            
            point = PointStruct(
                id=i,
                vector=embedding.tolist(),
                payload=payload
            )
            points.append(point)
        
        # Upload points in batches
        batch_size = 100
        for i in range(0, len(points), batch_size):
            batch = points[i:i+batch_size]
            self.qdrant_client.upsert(
                collection_name=self.collection_name,
                points=batch
            )
            logger.info(f"Uploaded batch {i//batch_size + 1}/{(len(points)-1)//batch_size + 1}")
        
        logger.info(f"Stored {len(embeddings)} embeddings in Qdrant")
        if embeddings and isinstance(embeddings[0][0], MedicalRecordChunk):
            logger.info(f"Embeddings are chunk-based for granular search")
    def process_dataset(self, dataset_path: str) -> List[MedicalRecord]:
        """
        Process entire dataset of medical records
        
        Args:
            dataset_path: Path to dataset directory
            
        Returns:
            List of processed medical records
        """
        dataset_path = Path(dataset_path)
        md_files = list(dataset_path.glob("*.md"))
        
        logger.info(f"Found {len(md_files)} markdown files")
        
        # Parse all records
        records = []
        for file_path in md_files:
            record = self.parse_medical_record(str(file_path))
            if record:
                records.append(record)
        
        logger.info(f"Successfully parsed {len(records)} records")
        
        # Deduplicate
        unique_records = self.deduplicate_records(records)
        logger.info(f"After deduplication: {len(unique_records)} unique records")
        
        return unique_records
    
    def query_similar_records(self, query_text: str, limit: int = 10) -> List[Dict]:
        """
        Query for similar medical records or chunks
        
        Args:
            query_text: Search query
            limit: Maximum number of results
            
        Returns:
            List of similar records/chunks with scores
        """
        if not self.qdrant_client:
            raise ValueError("Qdrant client not initialized")
        
        # Create query embedding
        query_embedding = self.model.encode(query_text)
        
        # Search in Qdrant
        search_result = self.qdrant_client.search(
            collection_name=self.collection_name,
            query_vector=query_embedding.tolist(),
            limit=limit
        )
        
        results = []
        for result in search_result:
            payload = result.payload
            
            if payload.get('is_chunk', False):
                # Handle chunk results
                result_info = {
                    'score': result.score,
                    'type': 'chunk',
                    'chunk_type': payload.get('chunk_type', 'unknown'),
                    'chunk_id': payload.get('chunk_id', 'unknown'),
                    'content': payload.get('content', '')[:200] + '...' if len(payload.get('content', '')) > 200 else payload.get('content', ''),
                    'file_path': payload.get('parent_file_path', 'unknown'),
                    'patient_id': payload.get('parent_patient_id', 'unknown'),
                    'record_type': payload.get('parent_record_type', 'unknown'),
                    'primary_diagnosis': payload.get('parent_primary_diagnosis', 'unknown'),
                    'department': payload.get('parent_department', 'unknown'),
                    'summary': f"{payload.get('chunk_type', 'unknown')} - {payload.get('parent_primary_diagnosis', 'unknown')}"
                }
            else:
                # Handle full record results
                result_info = {
                    'score': result.score,
                    'type': 'record',
                    'record': payload,
                    'file_path': payload.get('file_path', 'unknown'),
                    'patient_id': payload.get('patient_id', 'unknown'),
                    'summary': f"{payload.get('record_type', 'unknown')} - {payload.get('primary_diagnosis', 'unknown')}"
                }
            
            results.append(result_info)
        
        return results
    
    def analyze_record_similarity(self, records: List[MedicalRecord], similarity_threshold: float = 0.8) -> Dict:
        """
        Analyze similarity between records without removing any
        This helps identify similar content while preserving all files
        
        Args:
            records: List of medical records to analyze
            similarity_threshold: Threshold for considering records similar
            
        Returns:
            Dictionary with similarity analysis results
        """
        analysis = {
            'total_records': len(records),
            'exact_duplicates': {},
            'similar_groups': [],
            'unique_records': 0,
            'similarity_matrix': {}
        }
        
        # Group by exact content hash
        hash_groups = {}
        for record in records:
            if record.content_hash not in hash_groups:
                hash_groups[record.content_hash] = []
            hash_groups[record.content_hash].append(record)
        
        # Find exact duplicates
        for hash_key, group in hash_groups.items():
            if len(group) > 1:
                file_paths = [r.file_path for r in group]
                analysis['exact_duplicates'][hash_key[:8]] = file_paths
        
        # Find similar (but not identical) records
        processed_pairs = set()
        for i, record1 in enumerate(records):
            for j, record2 in enumerate(records[i+1:], i+1):
                pair_key = tuple(sorted([record1.file_path, record2.file_path]))
                if pair_key in processed_pairs:
                    continue
                processed_pairs.add(pair_key)
                
                # Skip if they're exact duplicates
                if record1.content_hash == record2.content_hash:
                    continue
                
                similarity = self._calculate_similarity(
                    record1.specialized_content,
                    record2.specialized_content
                )
                
                if similarity >= similarity_threshold:
                    # Find existing group or create new one
                    for group in analysis['similar_groups']:
                        if record1.file_path in group['files'] or record2.file_path in group['files']:
                            if record1.file_path not in group['files']:
                                group['files'].append(record1.file_path)
                            if record2.file_path not in group['files']:
                                group['files'].append(record2.file_path)
                            group['similarities'].append({
                                'file1': record1.file_path,
                                'file2': record2.file_path,
                                'similarity': similarity
                            })
                            break
                    else:
                        analysis['similar_groups'].append({
                            'files': [record1.file_path, record2.file_path],
                            'similarities': [{
                                'file1': record1.file_path,
                                'file2': record2.file_path,
                                'similarity': similarity
                            }]
                        })
        
        analysis['unique_records'] = len(hash_groups)
        
        logger.info(f"Similarity Analysis Complete:")
        logger.info(f"  - Total records: {analysis['total_records']}")
        logger.info(f"  - Unique content hashes: {analysis['unique_records']}")
        logger.info(f"  - Exact duplicate groups: {len(analysis['exact_duplicates'])}")
        logger.info(f"  - Similar (not identical) groups: {len(analysis['similar_groups'])}")
        
        return analysis
    
    def find_content_in_files(self, search_text: str, records: List[MedicalRecord] = None) -> List[Dict]:
        """
        Find which specific files contain given text content
        
        Args:
            search_text: Text to search for
            records: List of records to search in (if None, will process dataset)
            
        Returns:
            List of files containing the search text with match details
        """
        if records is None:
            records = self.process_dataset("d:/Chatbot_hospital/dataset")
        
        matches = []
        search_lower = search_text.lower()
        
        for record in records:
            # Search in different content areas
            content_areas = {
                'specialized_content': record.specialized_content,
                'primary_diagnosis': record.primary_diagnosis,
                'clinical_notes': ' '.join(record.clinical_notes),
                'medications': ' '.join(record.medications),
                'full_file': ''
            }
            
            # Also search in the full original file
            try:
                with open(record.file_path, 'r', encoding='utf-8') as f:
                    content_areas['full_file'] = f.read()
            except:
                pass
            
            match_info = {
                'file_path': record.file_path,
                'patient_id': record.patient_id,
                'record_type': record.record_type,
                'matches': []
            }
            
            for area_name, content in content_areas.items():
                if content and search_lower in content.lower():
                    # Find the context around the match
                    start_idx = content.lower().find(search_lower)
                    context_start = max(0, start_idx - 100)
                    context_end = min(len(content), start_idx + len(search_text) + 100)
                    context = content[context_start:context_end]
                    
                    match_info['matches'].append({
                        'area': area_name,
                        'context': context,
                        'position': start_idx
                    })
            
            if match_info['matches']:
                matches.append(match_info)
        
        return matches

    def get_file_specific_embedding(self, file_path: str) -> Optional[np.ndarray]:
        """
        Get the embedding for a specific file
        
        Args:
            file_path: Path to the specific file
            
        Returns:
            Embedding vector for the file or None if not found
        """
        try:
            record = self.parse_medical_record(file_path)
            if record:
                embedding_text = self._create_embedding_text(record)
                return self.model.encode(embedding_text)
            return None
        except Exception as e:
            logger.error(f"Error getting embedding for {file_path}: {e}")
            return None

    def chunk_medical_record(self, record: MedicalRecord) -> List[MedicalRecordChunk]:
        """
        Split a medical record into focused chunks for granular embeddings
        
        Args:
            record: Medical record to chunk
            
        Returns:
            List of medical record chunks
        """
        chunks = []
        
        # Create patient/header chunk
        header_content = self._create_header_chunk_content(record)
        chunks.append(MedicalRecordChunk(
            chunk_id=f"{record.content_hash}_header",
            parent_record=record,
            chunk_type="header",
            content=header_content,
            chunk_index=0,
            metadata={
                "patient_id": record.patient_id,
                "file_path": record.file_path,
                "record_type": record.record_type,
                "department": record.department
            }
        ))
        
        # Create diagnosis chunk
        if record.primary_diagnosis or record.secondary_diagnoses:
            diagnosis_content = self._create_diagnosis_chunk_content(record)
            chunks.append(MedicalRecordChunk(
                chunk_id=f"{record.content_hash}_diagnosis",
                parent_record=record,
                chunk_type="diagnosis",
                content=diagnosis_content,
                chunk_index=len(chunks),
                metadata={
                    "patient_id": record.patient_id,
                    "file_path": record.file_path,
                    "primary_diagnosis": record.primary_diagnosis
                }
            ))
        
        # Create clinical notes chunks (may be split if too long)
        clinical_chunks = self._create_clinical_notes_chunks(record, len(chunks))
        chunks.extend(clinical_chunks)
        
        # Create medications chunk
        if record.medications:
            medications_content = self._create_medications_chunk_content(record)
            chunks.append(MedicalRecordChunk(
                chunk_id=f"{record.content_hash}_medications",
                parent_record=record,
                chunk_type="medications",
                content=medications_content,
                chunk_index=len(chunks),
                metadata={
                    "patient_id": record.patient_id,
                    "file_path": record.file_path,
                    "medications": ', '.join(record.medications)
                }
            ))
        
        # Create procedures chunk
        if record.procedures:
            procedures_content = self._create_procedures_chunk_content(record)
            chunks.append(MedicalRecordChunk(
                chunk_id=f"{record.content_hash}_procedures",
                parent_record=record,
                chunk_type="procedures",
                content=procedures_content,
                chunk_index=len(chunks),
                metadata={
                    "patient_id": record.patient_id,
                    "file_path": record.file_path,
                    "procedures": ', '.join(record.procedures)
                }
            ))
        
        # Create specialized content chunks (if different from clinical notes)
        if record.specialized_content:
            specialized_chunks = self._create_specialized_content_chunks(record, len(chunks))
            chunks.extend(specialized_chunks)
        
        return chunks
    
    def _create_header_chunk_content(self, record: MedicalRecord) -> str:
        """Create header chunk with patient and administrative info"""
        components = [
            f"Patient: {record.patient_id}",
            f"Record Type: {record.record_type}",
            f"Department: {record.department}",
            f"Physician: {record.physician}"
        ]
        
        if record.admission_date:
            components.append(f"Admission Date: {record.admission_date}")
        if record.discharge_date:
            components.append(f"Discharge Date: {record.discharge_date}")
        
        # Add vital signs if available
        if record.vital_signs:
            vital_info = []
            for key, value in record.vital_signs.items():
                vital_info.append(f"{key}: {value}")
            components.append(f"Vital Signs: {'; '.join(vital_info)}")
        
        # Add allergies if available
        if record.allergies:
            components.append(f"Allergies: {', '.join(record.allergies)}")
        
        return '. '.join(components)
    
    def _create_diagnosis_chunk_content(self, record: MedicalRecord) -> str:
        """Create diagnosis-focused chunk"""
        components = [
            f"Patient: {record.patient_id}",
            f"Primary Diagnosis: {record.primary_diagnosis}"
        ]
        
        if record.secondary_diagnoses:
            components.append(f"Secondary Diagnoses: {', '.join(record.secondary_diagnoses)}")
        
        return '. '.join(components)
    
    def _create_clinical_notes_chunks(self, record: MedicalRecord, start_index: int) -> List[MedicalRecordChunk]:
        """Create clinical notes chunks, splitting if too long"""
        chunks = []
        
        if not record.clinical_notes:
            return chunks
        
        # Combine all clinical notes
        combined_notes = ' '.join(record.clinical_notes)
        
        # Split into chunks if too long
        note_chunks = self._split_text_into_chunks(combined_notes, self.max_chunk_size)
        
        for i, chunk_content in enumerate(note_chunks):
            chunk_text = f"Patient: {record.patient_id}. Clinical Examination: {chunk_content}"
            
            chunks.append(MedicalRecordChunk(
                chunk_id=f"{record.content_hash}_clinical_{i}",
                parent_record=record,
                chunk_type="clinical_notes",
                content=chunk_text,
                chunk_index=start_index + i,
                metadata={
                    "patient_id": record.patient_id,
                    "file_path": record.file_path,
                    "chunk_part": f"{i+1}/{len(note_chunks)}"
                }
            ))
        
        return chunks
    
    def _create_medications_chunk_content(self, record: MedicalRecord) -> str:
        """Create medications-focused chunk"""
        components = [
            f"Patient: {record.patient_id}",
            f"Medications: {', '.join(record.medications)}"
        ]
        
        if record.allergies:
            components.append(f"Allergies: {', '.join(record.allergies)}")
        
        return '. '.join(components)
    
    def _create_procedures_chunk_content(self, record: MedicalRecord) -> str:
        """Create procedures-focused chunk"""
        components = [
            f"Patient: {record.patient_id}",
            f"Procedures: {', '.join(record.procedures)}"
        ]
        
        if record.primary_diagnosis:
            components.append(f"Primary Diagnosis: {record.primary_diagnosis}")
        
        return '. '.join(components)
    
    def _create_specialized_content_chunks(self, record: MedicalRecord, start_index: int) -> List[MedicalRecordChunk]:
        """Create specialized content chunks, splitting if too long"""
        chunks = []
        
        if not record.specialized_content:
            return chunks
        
        # Skip if specialized content is very similar to clinical notes
        if record.clinical_notes and self._is_content_similar(
            record.specialized_content, ' '.join(record.clinical_notes)
        ):
            return chunks
        
        # Split specialized content into chunks
        content_chunks = self._split_text_into_chunks(record.specialized_content, self.max_chunk_size)
        
        for i, chunk_content in enumerate(content_chunks):
            chunk_text = f"Patient: {record.patient_id}. Medical Content: {chunk_content}"
            
            chunks.append(MedicalRecordChunk(
                chunk_id=f"{record.content_hash}_specialized_{i}",
                parent_record=record,
                chunk_type="specialized_content",
                content=chunk_text,
                chunk_index=start_index + i,
                metadata={
                    "patient_id": record.patient_id,
                    "file_path": record.file_path,
                    "chunk_part": f"{i+1}/{len(content_chunks)}"
                }
            ))
        
        return chunks
    
    def _split_text_into_chunks(self, text: str, max_size: int) -> List[str]:
        """Split text into chunks of maximum size (in words)"""
        words = text.split()
        
        if len(words) <= max_size:
            return [text]
        
        chunks = []
        current_chunk = []
        
        for word in words:
            if len(current_chunk) >= max_size:
                chunks.append(' '.join(current_chunk))
                current_chunk = []
            current_chunk.append(word)
        
        if current_chunk:
            chunks.append(' '.join(current_chunk))
        
        return chunks
    
    def _is_content_similar(self, content1: str, content2: str, threshold: float = 0.7) -> bool:
        """Check if two content strings are similar enough to avoid duplication"""
        similarity = self._calculate_similarity(content1, content2)
        return similarity > threshold

    def reset_collection(self, confirm: bool = False):
        """
        Reset the Qdrant collection by deleting and recreating it
        
        Args:
            confirm: If True, skip confirmation prompt
        """
        if not self.qdrant_client:
            raise ValueError("Qdrant client not initialized. Call setup_qdrant() first.")
        
        if not confirm:
            response = input(f"Are you sure you want to delete collection '{self.collection_name}'? (yes/no): ")
            if response.lower() != 'yes':
                logger.info("Collection reset cancelled")
                return
        
        try:
            # Delete existing collection
            self.qdrant_client.delete_collection(collection_name=self.collection_name)
            logger.info(f"Deleted collection: {self.collection_name}")
            
            # Recreate collection
            self.qdrant_client.create_collection(
                collection_name=self.collection_name,
                vectors_config=VectorParams(
                    size=self.model.get_sentence_embedding_dimension(),
                    distance=Distance.COSINE
                )
            )
            logger.info(f"Recreated collection: {self.collection_name}")
            
        except Exception as e:
            logger.error(f"Error resetting collection: {e}")
            raise
    
    def clear_collection(self, confirm: bool = False):
        """
        Clear all data from the collection without deleting the collection
        
        Args:
            confirm: If True, skip confirmation prompt
        """
        if not self.qdrant_client:
            raise ValueError("Qdrant client not initialized. Call setup_qdrant() first.")
        
        if not confirm:
            response = input(f"Are you sure you want to clear all data from '{self.collection_name}'? (yes/no): ")
            if response.lower() != 'yes':
                logger.info("Collection clear cancelled")
                return
        
        try:
            # Get all points
            points, _ = self.qdrant_client.scroll(
                collection_name=self.collection_name,
                limit=10000,
                with_payload=False,
                with_vectors=False
            )
            
            if points:
                # Delete all points
                point_ids = [point.id for point in points]
                self.qdrant_client.delete(
                    collection_name=self.collection_name,
                    points_selector=point_ids
                )
                logger.info(f"Cleared {len(point_ids)} points from collection")
            else:
                logger.info("Collection is already empty")
                
        except Exception as e:
            logger.error(f"Error clearing collection: {e}")
            raise
    
    def get_collection_stats(self) -> dict:
        """
        Get statistics about the current collection
        
        Returns:
            Dictionary with collection statistics
        """
        if not self.qdrant_client:
            raise ValueError("Qdrant client not initialized. Call setup_qdrant() first.")
        
        try:
            # Check if collection exists
            collections = self.qdrant_client.get_collections()
            collection_names = [col.name for col in collections.collections]
            
            if self.collection_name not in collection_names:
                return {"exists": False, "count": 0}
            
            # Get collection info
            info = self.qdrant_client.get_collection(collection_name=self.collection_name)
            count = self.qdrant_client.count(collection_name=self.collection_name)
            
            # Get chunk type distribution if data exists
            chunk_distribution = {}
            if count.count > 0:
                # Sample some points to analyze chunk types
                points, _ = self.qdrant_client.scroll(
                    collection_name=self.collection_name,
                    limit=min(1000, count.count),
                    with_payload=True,
                    with_vectors=False
                )
                
                for point in points:
                    if 'chunk_type' in point.payload:
                        chunk_type = point.payload['chunk_type']
                        chunk_distribution[chunk_type] = chunk_distribution.get(chunk_type, 0) + 1
            
            return {
                "exists": True,
                "count": count.count,
                "vector_size": info.config.params.vectors.size,
                "distance_metric": str(info.config.params.vectors.distance),
                "chunk_distribution": chunk_distribution,
                "chunking_enabled": len(chunk_distribution) > 0
            }
            
        except Exception as e:
            logger.error(f"Error getting collection stats: {e}")
            return {"exists": False, "error": str(e)}
    
    def update_database(self, dataset_path: str = None, reset_method: str = "clear"):
        """
        Update the database with fresh data from the dataset
        
        Args:
            dataset_path: Path to dataset (if None, uses default)
            reset_method: "clear" (clear data) or "reset" (recreate collection)
        """
        dataset_path = dataset_path or "d:/Chatbot_hospital/dataset"
        
        logger.info(f"Updating database with method: {reset_method}")
        
        # Reset/clear the collection
        if reset_method == "reset":
            self.reset_collection(confirm=True)
        elif reset_method == "clear":
            self.clear_collection(confirm=True)
        else:
            raise ValueError("reset_method must be 'clear' or 'reset'")
        
        # Process and store new data
        logger.info("Processing dataset...")
        records = self.process_dataset(dataset_path)
        
        logger.info("Creating embeddings...")
        embeddings = self.create_embeddings(records)
        
        logger.info("Storing in database...")
        self.store_in_qdrant(embeddings)
        
        # Show final stats
        stats = self.get_collection_stats()
        logger.info(f"Database update completed. Total points: {stats['count']}")
        
        return stats

def main():
    """Main function to demonstrate the embedder with chunking"""
    print("=== Medical Record Embedder with Section Chunking ===\n")
    
    # Initialize embedder with chunking enabled
    embedder = MedicalRecordEmbedder(enable_chunking=True, max_chunk_size=300)
    print(f"Chunking enabled: {embedder.enable_chunking}")
    print(f"Max chunk size: {embedder.max_chunk_size} words\n")
    
    # Setup Qdrant (make sure Qdrant is running)
    try:
        embedder.setup_qdrant()
        logger.info("Qdrant setup successful")
    except Exception as e:
        logger.error(f"Qdrant setup failed: {e}")
        return
    
    # Process dataset
    dataset_path = "d:/Chatbot_hospital/dataset"
    records = embedder.process_dataset(dataset_path)
    
    # Analyze similarity without removing files
    similarity_analysis = embedder.analyze_record_similarity(records)
    
    # Create embeddings for ALL records (with chunking for granular search)
    embeddings = embedder.create_embeddings(records)
    
    # Store in Qdrant
    embedder.store_in_qdrant(embeddings)
    
    logger.info("Embedding process completed successfully!")
    logger.info(f"Stored {len(embeddings)} chunk embeddings from {len(records)} records")
    
    # Demonstrate different types of queries
    print("\n=== CHUNKED SEARCH DEMONSTRATION ===\n")
    
    # Query 1: Diagnosis-focused search
    print("1. Diagnosis-focused search:")
    query1 = "patient with heart problems and chest pain"
    results1 = embedder.query_similar_records(query1, limit=5)
    print(f"Query: {query1}")
    for result in results1:
        file_name = Path(result['file_path']).name
        if result['type'] == 'chunk':
            print(f"  - Score: {result['score']:.3f} - {result['chunk_type']} chunk - {result['summary']} - File: {file_name}")
        else:
            print(f"  - Score: {result['score']:.3f} - Full record - {result['summary']} - File: {file_name}")
    
    # Query 2: Medication-focused search
    print("\n2. Medication-focused search:")
    query2 = "patient taking medication for pain management"
    results2 = embedder.query_similar_records(query2, limit=5)
    print(f"Query: {query2}")
    for result in results2:
        file_name = Path(result['file_path']).name
        if result['type'] == 'chunk':
            print(f"  - Score: {result['score']:.3f} - {result['chunk_type']} chunk - {result['summary']} - File: {file_name}")
        else:
            print(f"  - Score: {result['score']:.3f} - Full record - {result['summary']} - File: {file_name}")
    
    # Query 3: Clinical examination search
    print("\n3. Clinical examination search:")
    query3 = "physical examination findings and vital signs"
    results3 = embedder.query_similar_records(query3, limit=5)
    print(f"Query: {query3}")
    for result in results3:
        file_name = Path(result['file_path']).name
        if result['type'] == 'chunk':
            print(f"  - Score: {result['score']:.3f} - {result['chunk_type']} chunk - {result['summary']} - File: {file_name}")
        else:
            print(f"  - Score: {result['score']:.3f} - Full record - {result['summary']} - File: {file_name}")
    
    # Demonstrate chunk analysis
    print(f"\n=== CHUNKING ANALYSIS ===")
    if embeddings and isinstance(embeddings[0][0], MedicalRecordChunk):
        chunk_types = {}
        for chunk, _ in embeddings:
            chunk_type = chunk.chunk_type
            if chunk_type not in chunk_types:
                chunk_types[chunk_type] = 0
            chunk_types[chunk_type] += 1
        
        print("Chunk distribution:")
        for chunk_type, count in sorted(chunk_types.items()):
            print(f"  - {chunk_type}: {count} chunks")
    
    # Demonstration of content search
    print(f"\n=== CONTENT SEARCH DEMONSTRATION ===")
    search_content = "chest pain"
    content_matches = embedder.find_content_in_files(search_content, records)
    print(f"Files containing '{search_content}':")
    for match in content_matches[:3]:  # Show first 3 matches
        print(f"  - {Path(match['file_path']).name}: {len(match['matches'])} matches")
    
    print(f"\n=== BENEFITS OF CHUNKING ===")
    print("- More granular search results")
    print("- Better matching of specific medical aspects")
    print("- Improved search accuracy for focused queries")
    print("- Preservation of file-level traceability")
    print("- Enhanced semantic understanding of medical content")

if __name__ == "__main__":
    main()
