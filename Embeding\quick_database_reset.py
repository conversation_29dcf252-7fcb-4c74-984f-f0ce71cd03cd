"""
Quick Database Reset and Update Utility for Medical Records Search

This script fixes the search compatibility issues and provides
easy database reset and update functionality.
"""

import os
import sys
from pathlib import Path

# Add the current directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from medical_record_embedder import MedicalRecordEmbedder
from config import *

def quick_reset_and_update():
    """Quick reset and update function"""
    print("🔄 Quick Database Reset and Update")
    print("=" * 40)
    
    # Initialize embedder
    embedder = MedicalRecordEmbedder(
        model_name=EMBEDDING_MODEL,
        enable_chunking=True,
        max_chunk_size=300
    )
    
    try:
        # Setup Qdrant connection
        print("🔗 Connecting to Qdrant...")
        embedder.setup_qdrant(host=QDRANT_HOST, port=QDRANT_PORT)
        
        # Clear existing data
        print("🗑️  Clearing existing data...")
        try:
            embedder.qdrant_client.delete(
                collection_name=embedder.collection_name,
                points_selector={"filter": {"must": [{"key": "is_chunk", "match": {"any": [True, False]}}]}}
            )
            print("✅ Existing data cleared")
        except Exception as e:
            print(f"ℹ️  Collection might be empty: {e}")
        
        # Process dataset
        print(f"📚 Processing medical records from: {DATASET_PATH}")
        records = embedder.process_dataset(DATASET_PATH)
        
        if not records:
            print("❌ No records found to process")
            return False
        
        print(f"📊 Processing {len(records)} unique records...")
        
        # Create embeddings with chunking
        print("🧠 Creating embeddings with chunking...")
        embeddings = embedder.create_embeddings(records)
        print(f"✅ Created {len(embeddings)} chunk embeddings")
        
        # Store in Qdrant
        print("💾 Storing embeddings in vector database...")
        embedder.store_in_qdrant(embeddings)
        
        # Test the update
        print("🔍 Testing search functionality...")
        test_results = embedder.query_similar_records("patient", limit=3)
        
        if test_results:
            print(f"✅ Database update successful! Found {len(test_results)} test results")
            print("\nSample results:")
            for i, result in enumerate(test_results[:2], 1):
                if result['type'] == 'chunk':
                    file_name = Path(result['file_path']).name
                    print(f"   {i}. {result['chunk_type']} - {result['primary_diagnosis']} - {file_name}")
                else:
                    print(f"   {i}. Legacy format result")
        else:
            print("⚠️  No test results found")
        
        print("\n🎉 Database reset and update completed successfully!")
        print("You can now use the search tool normally.")
        return True
        
    except Exception as e:
        print(f"❌ Error during reset and update: {e}")
        import traceback
        traceback.print_exc()
        return False

def show_database_info():
    """Show current database information"""
    print("📊 Database Information")
    print("=" * 30)
    
    embedder = MedicalRecordEmbedder(enable_chunking=True)
    
    try:
        embedder.setup_qdrant(host=QDRANT_HOST, port=QDRANT_PORT)
        
        # Get collection info
        collection_info = embedder.qdrant_client.get_collection(
            collection_name=embedder.collection_name
        )
        
        print(f"Collection: {embedder.collection_name}")
        print(f"Total vectors: {collection_info.vectors_count}")
        print(f"Vector size: {collection_info.config.params.vectors.size}")
        
        # Get sample results to analyze
        sample_results = embedder.query_similar_records("patient", limit=50)
        
        if sample_results:
            chunk_types = {}
            files = set()
            
            for result in sample_results:
                if result['type'] == 'chunk':
                    chunk_type = result['chunk_type']
                    chunk_types[chunk_type] = chunk_types.get(chunk_type, 0) + 1
                    files.add(result['file_path'])
            
            print(f"Unique files: {len(files)}")
            print(f"Sample results: {len(sample_results)}")
            
            if chunk_types:
                print("\nChunk distribution:")
                for chunk_type, count in sorted(chunk_types.items()):
                    print(f"  • {chunk_type.replace('_', ' ').title()}: {count}")
        else:
            print("No data found in collection")
            
    except Exception as e:
        print(f"❌ Error getting database info: {e}")

def main():
    """Main function"""
    print("🏥 Medical Records Database Utility")
    print("=" * 40)
    
    if len(sys.argv) > 1:
        command = sys.argv[1].lower()
        
        if command in ['reset', 'update', 'clear']:
            quick_reset_and_update()
        elif command in ['info', 'status', 'stats']:
            show_database_info()
        elif command == 'help':
            print("\nAvailable commands:")
            print("  python quick_database_reset.py reset  - Reset and update database")
            print("  python quick_database_reset.py info   - Show database information")
            print("  python quick_database_reset.py help   - Show this help")
        else:
            print(f"❌ Unknown command: {command}")
            print("Use 'help' to see available commands")
    else:
        # Interactive mode
        while True:
            print("\nOptions:")
            print("1. Reset and update database")
            print("2. Show database information")
            print("3. Exit")
            
            try:
                choice = input("\nEnter your choice (1-3): ").strip()
                
                if choice == '1':
                    quick_reset_and_update()
                elif choice == '2':
                    show_database_info()
                elif choice == '3':
                    print("👋 Goodbye!")
                    break
                else:
                    print("❌ Invalid choice")
                    
            except KeyboardInterrupt:
                print("\n👋 Goodbye!")
                break

if __name__ == "__main__":
    main()
