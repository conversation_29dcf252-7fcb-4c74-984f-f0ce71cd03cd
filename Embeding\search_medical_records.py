"""
Interactive Medical Record Search Tool

This script provides an interactive search interface for querying
medical records using the improved embedding system.
"""

import os
import sys
import json
from pathlib import Path
from typing import List, Dict, Optional
import readline  # For better input handling

# Add the current directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from medical_record_embedder import MedicalRecordEmbedder
from config import *

class MedicalRecordSearch:
    """Interactive search tool for medical records"""
    
    def __init__(self):
        self.embedder = MedicalRecordEmbedder(model_name=EMBEDDING_MODEL)
        self.is_initialized = False
        self.total_records = 0
        
    def initialize_search_system(self):
        """Initialize the search system with embeddings"""
        print("🔧 Initializing medical record search system...")
        
        try:
            # Setup Qdrant connection
            self.embedder.setup_qdrant(host=QDRANT_HOST, port=QDRANT_PORT)
            
            # Check if embeddings already exist
            try:
                # Try a test query to see if data exists
                test_results = self.embedder.query_similar_records("test", limit=1)
                if test_results:
                    self.total_records = len(test_results)
                    print(f"✅ Found existing embeddings with records")
                    self.is_initialized = True
                    return True
            except:
                pass
            
            # If no existing embeddings, create them
            print("📚 Processing medical records and creating embeddings...")
            records = self.embedder.process_dataset(DATASET_PATH)
            self.total_records = len(records)
            
            if not records:
                print("❌ No medical records found to process")
                return False
            
            print(f"📊 Processing {len(records)} unique records...")
            embeddings = self.embedder.create_embeddings(records)
            
            print("💾 Storing embeddings in vector database...")
            self.embedder.store_in_qdrant(embeddings)
            
            print("✅ Search system initialized successfully!")
            self.is_initialized = True
            return True
            
        except Exception as e:
            print(f"❌ Error initializing search system: {e}")
            print("Make sure Qdrant is running (use: python setup_qdrant.py)")
            return False
    
    def search_records(self, query: str, limit: int = 5, min_score: float = 0.3) -> List[Dict]:
        """
        Search for medical records matching the query
        
        Args:
            query: Search query
            limit: Maximum number of results
            min_score: Minimum similarity score (0-1)
            
        Returns:
            List of matching records with metadata
        """
        if not self.is_initialized:
            print("❌ Search system not initialized")
            return []
        
        try:
            results = self.embedder.query_similar_records(query, limit=limit)
            
            # Filter by minimum score
            filtered_results = [r for r in results if r['score'] >= min_score]
            
            return filtered_results
            
        except Exception as e:
            print(f"❌ Search error: {e}")
            return []
    
    def display_search_results(self, results: List[Dict], query: str):
        """Display search results in a formatted way"""
        if not results:
            print("❌ No relevant results found")
            return
        
        print(f"\n🔍 Search Results for: '{query}'")
        print("=" * 60)
        
        for i, result in enumerate(results, 1):
            score = result['score']
            
            # Handle both chunked and non-chunked results
            if result['type'] == 'chunk':
                # New chunked format
                chunk_type = result['chunk_type']
                patient_id = result['patient_id']
                file_path = result['file_path']
                record_type = result['record_type']
                primary_diagnosis = result['primary_diagnosis']
                department = result['department']
                content = result['content']
                
                print(f"\n{i}. Score: {score:.3f} | Type: {chunk_type.upper()} | File: {Path(file_path).name}")
                print(f"   Patient: {patient_id}")
                print(f"   Record Type: {record_type.capitalize()}")
                print(f"   Primary Diagnosis: {primary_diagnosis}")
                print(f"   Department: {department}")
                print(f"   Content: {content}")
                
            else:
                # Old full record format (fallback)
                record = result.get('record', {})
                
                print(f"\n{i}. Score: {score:.3f} | File: {Path(record.get('file_path', 'unknown')).name}")
                print(f"   Patient: {record.get('patient_id', 'unknown')}")
                print(f"   Type: {record.get('record_type', 'unknown').capitalize()}")
                print(f"   Diagnosis: {record.get('primary_diagnosis', 'unknown')}")
                
                if record.get('medications'):
                    meds = ', '.join(record['medications'][:3])
                    if len(record['medications']) > 3:
                        meds += f" (+{len(record['medications'])-3} more)"
                    print(f"   Medications: {meds}")
                
                if record.get('allergies'):
                    print(f"   Allergies: {', '.join(record['allergies'])}")
                
                print(f"   Department: {record.get('department', 'unknown')}")
                print(f"   Physician: {record.get('physician', 'unknown')}")
                
                # Show a snippet of the specialized content
                content_snippet = record.get('specialized_content', '')[:200]
                if len(record.get('specialized_content', '')) > 200:
                    content_snippet += "..."
                print(f"   Content: {content_snippet}")
            
            print("-" * 60)
    
    def interactive_search(self):
        """Start interactive search session"""
        print("\n🏥 INTERACTIVE MEDICAL RECORD SEARCH")
        print("=" * 50)
        
        if not self.is_initialized:
            if not self.initialize_search_system():
                return
        
        print(f"\n📚 Database contains {self.total_records} medical records")
        print("\nSearch Tips:")
        print("• Use medical terms: 'chest pain', 'diabetes', 'depression'")
        print("• Specify conditions: 'heart attack patient', 'suicide attempt'")
        print("• Include symptoms: 'abdominal pain', 'breathing problems'")
        print("• Try demographics: 'teenage patient', 'elderly diabetes'")
        print("• Type 'quit' or 'exit' to end")
        print("• Type 'help' for more options")
        
        while True:
            try:
                query = input("\n🔍 Enter search query: ").strip()
                
                if not query:
                    continue
                
                if query.lower() in ['quit', 'exit', 'q']:
                    print("👋 Goodbye!")
                    break
                
                if query.lower() == 'help':
                    self.show_help()
                    continue
                
                if query.lower().startswith('stats'):
                    self.show_statistics()
                    continue
                
                # Parse advanced search options
                limit = 5
                min_score = 0.3
                
                if ' --limit ' in query:
                    parts = query.split(' --limit ')
                    query = parts[0].strip()
                    try:
                        limit = int(parts[1].strip())
                    except:
                        print("⚠️  Invalid limit, using default (5)")
                
                if ' --score ' in query:
                    parts = query.split(' --score ')
                    query = parts[0].strip()
                    try:
                        min_score = float(parts[1].strip())
                    except:
                        print("⚠️  Invalid score, using default (0.3)")
                
                # Perform search
                results = self.search_records(query, limit=limit, min_score=min_score)
                self.display_search_results(results, query)
                
            except KeyboardInterrupt:
                print("\n👋 Search interrupted. Goodbye!")
                break
            except Exception as e:
                print(f"❌ Error: {e}")
    
    def show_help(self):
        """Show help information"""
        print("\n📖 SEARCH HELP")
        print("=" * 30)
        print("Search Examples:")
        print("• 'patient with heart problems'")
        print("• 'teenager depression suicide'")
        print("• 'diabetes high blood sugar'")
        print("• 'abdominal pain surgery'")
        print("• 'elderly breathing difficulty'")
        print("• 'medication overdose'")
        print("\nAdvanced Options:")
        print("• Add '--limit 10' to get more results")
        print("• Add '--score 0.5' to get higher quality results")
        print("• Type 'stats' to see database statistics")
        print("• Type 'quit' to exit")
    
    def show_statistics(self):
        """Show database statistics"""
        print("\n📊 DATABASE STATISTICS")
        print("=" * 30)
        
        try:
            # Get sample of records to analyze
            sample_results = self.embedder.query_similar_records("patient", limit=50)
            
            if not sample_results:
                print("No records found")
                return
            
            # Count specialties, departments, and chunk types
            specialties = {}
            departments = {}
            chunk_types = {}
            
            for result in sample_results:
                if result['type'] == 'chunk':
                    # New chunked format
                    specialty = result['record_type']
                    department = result['department']
                    chunk_type = result['chunk_type']
                    
                    specialties[specialty] = specialties.get(specialty, 0) + 1
                    departments[department] = departments.get(department, 0) + 1
                    chunk_types[chunk_type] = chunk_types.get(chunk_type, 0) + 1
                else:
                    # Old format (fallback)
                    record = result.get('record', {})
                    specialty = record.get('record_type', 'unknown')
                    department = record.get('department', 'unknown')
                    
                    specialties[specialty] = specialties.get(specialty, 0) + 1
                    departments[department] = departments.get(department, 0) + 1
            
            print(f"Total Results: {len(sample_results)}")
            
            if chunk_types:
                print("\nChunk Types:")
                for chunk_type, count in sorted(chunk_types.items(), key=lambda x: x[1], reverse=True):
                    print(f"  • {chunk_type.replace('_', ' ').title()}: {count}")
            
            print("\nTop Medical Specialties:")
            for specialty, count in sorted(specialties.items(), key=lambda x: x[1], reverse=True)[:5]:
                print(f"  • {specialty.capitalize()}: {count}")
            
            print("\nTop Departments:")
            for dept, count in sorted(departments.items(), key=lambda x: x[1], reverse=True)[:5]:
                print(f"  • {dept}: {count}")
                
        except Exception as e:
            print(f"Error getting statistics: {e}")
            import traceback
            traceback.print_exc()
    
    def batch_search(self, queries: List[str], output_file: str = "search_results.json"):
        """
        Perform batch search for multiple queries
        
        Args:
            queries: List of search queries
            output_file: File to save results
        """
        if not self.is_initialized:
            if not self.initialize_search_system():
                return
        
        print(f"🔍 Performing batch search for {len(queries)} queries...")
        
        all_results = {}
        
        for query in queries:
            print(f"Searching: {query}")
            results = self.search_records(query, limit=10)
            all_results[query] = []
            
            for result in results:
                if result['type'] == 'chunk':
                    # New chunked format
                    all_results[query].append({
                        'score': result['score'],
                        'chunk_type': result['chunk_type'],
                        'patient_id': result['patient_id'],
                        'diagnosis': result['primary_diagnosis'],
                        'record_type': result['record_type'],
                        'file_path': result['file_path'],
                        'content': result['content'][:200] + '...' if len(result['content']) > 200 else result['content']
                    })
                else:
                    # Old format (fallback)
                    record = result.get('record', {})
                    all_results[query].append({
                        'score': result['score'],
                        'patient_id': record.get('patient_id', 'unknown'),
                        'diagnosis': record.get('primary_diagnosis', 'unknown'),
                        'record_type': record.get('record_type', 'unknown'),
                        'file_path': record.get('file_path', 'unknown')
                    })
        
        # Save results
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(all_results, f, ensure_ascii=False, indent=2)
        
        print(f"✅ Batch search completed. Results saved to {output_file}")

def main():
    """Main function"""
    search_tool = MedicalRecordSearch()
    
    # Check command line arguments
    if len(sys.argv) > 1:
        if sys.argv[1] == '--batch':
            # Batch search mode
            test_queries = [
                "patient with heart attack and chest pain",
                "teenage suicide attempt with medication",
                "abdominal pain and appendicitis",
                "diabetes patient with high glucose",
                "stroke patient neurological deficit",
                "elderly breathing problems",
                "depression anxiety medication",
                "emergency cardiac arrest",
                "psychiatric evaluation suicide risk",
                "surgical intervention appendix"
            ]
            search_tool.batch_search(test_queries)
        else:
            # Single query mode
            query = ' '.join(sys.argv[1:])
            if not search_tool.initialize_search_system():
                sys.exit(1)
            
            results = search_tool.search_records(query)
            search_tool.display_search_results(results, query)
    else:
        # Interactive mode
        search_tool.interactive_search()

if __name__ == "__main__":
    main()
