#!/usr/bin/env python3
"""
Test script to debug chart generation issue
"""

import pandas as pd
import plotly.express as px
import plotly.graph_objects as go

# Create the exact data from your screenshot
data = {
    'nom': ['<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>'],
    'prenom': ['<PERSON>', '<PERSON>', '<PERSON>', '<PERSON><PERSON>'],
    'specialite': ['Généraliste', 'Cardiologue', 'Dermatologue', 'Pneumologue'],
    'unique_patients': [6, 3, 1, 1]
}

df = pd.DataFrame(data)

print("=== ORIGINAL DATA ===")
print(df)
print(f"DataFrame shape: {df.shape}")
print(f"DataFrame columns: {list(df.columns)}")
print(f"unique_patients values: {df['unique_patients'].tolist()}")

# Test the LLM-generated code
print("\n=== TESTING LLM GENERATED CODE ===")

# This is the code the LLM generated
plotly_code = """
# Use actual data values directly
categories = ['<PERSON>', '<PERSON>', '<PERSON>', '<PERSON> Yan<PERSON>']
values = [6, 3, 1, 1]
fig = px.bar(x=categories, y=values, title='Number of Patients per Doctor')
fig.update_layout(xaxis_title="Doctor", yaxis_title="Patients")
"""

print("Generated code:")
print(plotly_code)

# Execute the code
exec_globals = {
    'df': df,
    'pd': pd,
    'px': px,
    'go': go
}

exec(plotly_code, exec_globals)
fig = exec_globals.get('fig')

print(f"\n=== FIGURE DATA ===")
if fig and hasattr(fig, 'data') and fig.data:
    for i, trace in enumerate(fig.data):
        print(f"Trace {i}:")
        print(f"  x: {getattr(trace, 'x', 'N/A')}")
        print(f"  y: {getattr(trace, 'y', 'N/A')}")
        print(f"  type: {getattr(trace, 'type', 'N/A')}")

# Test alternative approach with DataFrame
print("\n=== TESTING DATAFRAME APPROACH ===")

plotly_code_df = """
df['full_name'] = df['nom'] + ' ' + df['prenom']
fig2 = px.bar(df, x='full_name', y='unique_patients', title='Number of Patients per Doctor (DataFrame)')
fig2.update_layout(xaxis_title="Doctor", yaxis_title="Patients")
"""

print("DataFrame code:")
print(plotly_code_df)

exec(plotly_code_df, exec_globals)
fig2 = exec_globals.get('fig2')

print(f"\n=== FIGURE 2 DATA ===")
if fig2 and hasattr(fig2, 'data') and fig2.data:
    for i, trace in enumerate(fig2.data):
        print(f"Trace {i}:")
        print(f"  x: {getattr(trace, 'x', 'N/A')}")
        print(f"  y: {getattr(trace, 'y', 'N/A')}")
        print(f"  type: {getattr(trace, 'type', 'N/A')}")

# Save figures to HTML for visual inspection
if fig:
    fig.write_html("test_chart_hardcoded.html")
    print("\nSaved hardcoded chart to test_chart_hardcoded.html")

if fig2:
    fig2.write_html("test_chart_dataframe.html")
    print("Saved DataFrame chart to test_chart_dataframe.html")

print("\n=== TEST COMPLETE ===")
