import traceback
import time
import io
import pandas as pd
import logging
from fastapi import APIRouter, HTTPException, Depends, Query
from fastapi.responses import StreamingResponse
from app.utils.dependencies import get_vanna, get_cache
from config import Config

router = APIRouter()
config = Config()
logger = logging.getLogger(__name__)

@router.get("/generate_summary_stream")
async def generate_summary_stream_api(
    id: str,
    vn = Depends(get_vanna),
    cache = Depends(get_cache)
):
    """Generate a streaming summary of the data results"""
    try:
        # Retrieve data from cache
        df = cache.get(id=id, field='df')
        question = cache.get(id=id, field='question')

        if df is None or question is None:
            raise HTTPException(status_code=404, detail={"type": "error", "error": f"Data not found in cache for id {id}"})

        # Fix: Ensure df is a proper DataFrame (same fix as in generate_plotly_figure)
        import pandas as pd
        if isinstance(df, str):
            try:
                # Try to convert string back to DataFrame
                import json
                df_data = json.loads(df)
                df = pd.DataFrame(df_data)

            except Exception as e:
                logger.error(f"Summary: Failed to convert string to DataFrame: {str(e)}")
                raise HTTPException(status_code=500, detail={"type": "error", "error": f"Invalid DataFrame format in cache: {str(e)}"})
        elif not isinstance(df, pd.DataFrame):

            raise HTTPException(status_code=500, detail={"type": "error", "error": f"Invalid data type in cache: expected DataFrame, got {type(df)}"})

        # Retrieve current chat history for the Vanna instance
        vn.chat_history = cache.get(id=id, field='chat_history') or []

        if df.empty:
            # For empty dataframes, return a simple non-streaming response
            def empty_response():
                import json
                yield f"data: {json.dumps({'type': 'summary_result', 'id': id, 'summary': 'No data available to summarize.', 'stream_end': True})}\n\n"

            return StreamingResponse(empty_response(), media_type='text/event-stream')
        else:
            # Use the streaming summary method
            response_stream = vn.generate_summary_stream(question=question, df=df, current_id=id)
            return StreamingResponse(response_stream, media_type='text/event-stream')

    except Exception as e:
        traceback.print_exc()
        def error_response():
            import json
            # Ensure id is available for error response
            error_id = id if 'id' in locals() else 'unknown'
            yield f"data: {json.dumps({'type': 'error', 'id': error_id, 'error': str(e), 'stream_end': True})}\n\n"

        return StreamingResponse(error_response(), media_type='text/event-stream')

@router.get("/generate_plotly_figure")
async def generate_plotly_figure_api(
    id: str,
    vn = Depends(get_vanna),
    cache = Depends(get_cache)
):
    """Generate a Plotly chart for the data"""
    try:
        # Retrieve data from cache
        df = cache.get(id=id, field='df')
        question = cache.get(id=id, field='question')
        sql = cache.get(id=id, field='sql')

        if df is None or question is None or sql is None:
             raise HTTPException(status_code=404, detail={"type": "error", "error": f"Required data not found in cache for id {id}"})

        # Fix: Ensure df is a proper DataFrame
        import pandas as pd
        if isinstance(df, str):
            try:
                # Try to convert string back to DataFrame
                import json
                df_data = json.loads(df)
                df = pd.DataFrame(df_data)

            except Exception as e:
                logger.error(f"Failed to convert string to DataFrame: {str(e)}")
                return {
                    "type": "error",
                    "id": id,
                    "error": f"Invalid DataFrame format in cache: {str(e)}"
                }
        elif not isinstance(df, pd.DataFrame):

            return {
                "type": "error",
                "id": id,
                "error": f"Invalid data type in cache: expected DataFrame, got {type(df)}"
            }



        # Retrieve current chat history for the Vanna instance
        vn.chat_history = cache.get(id=id, field='chat_history') or []

        if df.empty:
            return {
                "type": "error",
                "id": id,
                "error": "No data available to generate chart"
            }

        # Check if this was generated using intermediate SQL
        intermediate_sql = cache.get(id=id, field='intermediate_sql')
        is_intermediate_result = intermediate_sql is not None

        # Use Vanna's chart generation logic
        should_generate, plotly_code = vn.should_generate_chart_with_context(
            question=question,
            sql=sql,
            df=df,
            is_intermediate_sql=is_intermediate_result
        )

        if not should_generate or not plotly_code:
            return {
                "type": "no_chart",
                "id": id,
                "message": "Chart generation not recommended for this data"
            }

        # Execute the plotly code to generate the figure
        try:
            # Create a safe execution environment
            exec_globals = {
                'df': df,
                'pd': pd,
                'px': __import__('plotly.express', fromlist=['']),
                'go': __import__('plotly.graph_objects', fromlist=[''])
            }

            # Debug: Log the plotly code being executed
            logger.info(f"Executing plotly code:\n{plotly_code}")
            logger.info(f"DataFrame shape: {df.shape}")
            logger.info(f"DataFrame columns: {list(df.columns)}")
            logger.info(f"DataFrame data:\n{df.to_string()}")

            # Execute the plotly code
            exec(plotly_code, exec_globals)

            # Get the figure object
            fig = exec_globals.get('fig')
            if fig is None:
                raise ValueError("Plotly code did not create a 'fig' object")

            # Simplified approach: Just ensure data is in correct format
            try:
                # Convert any numpy arrays to regular Python lists
                for trace in fig.data:
                    if hasattr(trace, 'x') and hasattr(trace.x, 'tolist'):
                        trace.x = trace.x.tolist()
                    if hasattr(trace, 'y') and hasattr(trace.y, 'tolist'):
                        trace.y = trace.y.tolist()

                logger.info(f"Data conversion completed successfully")
            except Exception as e:
                logger.error(f"Error in data conversion: {str(e)}")
                # Continue with original figure if conversion fails

            # Debug: Log figure data
            logger.info(f"Figure created successfully")
            if hasattr(fig, 'data') and fig.data:
                for i, trace in enumerate(fig.data):
                    logger.info(f"Trace {i}: x={getattr(trace, 'x', 'N/A')}, y={getattr(trace, 'y', 'N/A')}")





            # Complete fix: Create JSON manually to avoid binary encoding
            import json

            try:
                # Create figure dictionary manually with guaranteed array format
                fig_dict = {
                    "data": [],
                    "layout": {}
                }

                # Process each trace manually to ensure proper data types
                for trace in fig.data:
                    # Extract data and ensure it's in list format
                    x_data = trace.x
                    y_data = trace.y

                    # Convert to Python lists (handles numpy arrays, tuples, etc.)
                    if hasattr(x_data, 'tolist'):
                        x_data = x_data.tolist()
                    elif hasattr(x_data, '__iter__') and not isinstance(x_data, str):
                        x_data = list(x_data)

                    if hasattr(y_data, 'tolist'):
                        y_data = y_data.tolist()
                    elif hasattr(y_data, '__iter__') and not isinstance(y_data, str):
                        y_data = list(y_data)

                    # Create trace dictionary with guaranteed array format
                    trace_dict = {
                        "type": getattr(trace, 'type', 'bar'),
                        "x": x_data,
                        "y": y_data,
                    }

                    # Add other common trace properties safely (avoid complex objects)
                    for attr in ['name', 'orientation', 'showlegend']:
                        if hasattr(trace, attr):
                            value = getattr(trace, attr)
                            if value is not None and isinstance(value, (str, bool, int, float)):
                                trace_dict[attr] = value

                    # Handle marker color only (avoid complex marker objects)
                    if hasattr(trace, 'marker') and trace.marker and hasattr(trace.marker, 'color'):
                        marker_color = getattr(trace.marker, 'color')
                        if marker_color is not None and isinstance(marker_color, str):
                            trace_dict['marker'] = {'color': marker_color}

                    fig_dict["data"].append(trace_dict)

                # Handle layout more safely
                if hasattr(fig, 'layout') and fig.layout:
                    layout_dict = {}

                    # Handle title
                    if hasattr(fig.layout, 'title') and fig.layout.title:
                        if hasattr(fig.layout.title, 'text'):
                            layout_dict['title'] = {"text": str(fig.layout.title.text)}

                    # Handle axes more safely
                    for axis_name in ['xaxis', 'yaxis']:
                        if hasattr(fig.layout, axis_name):
                            axis_obj = getattr(fig.layout, axis_name)
                            if axis_obj and hasattr(axis_obj, 'title') and axis_obj.title:
                                if hasattr(axis_obj.title, 'text'):
                                    layout_dict[axis_name] = {"title": {"text": str(axis_obj.title.text)}}

                    fig_dict["layout"] = layout_dict

                # Convert to JSON string
                fig_json = json.dumps(fig_dict)



            except Exception as e:


                # Simplified fallback: Just fix the data arrays and use to_json
                try:
                    # Ensure all trace data is in list format
                    for trace in fig.data:
                        if hasattr(trace, 'x') and hasattr(trace.x, 'tolist'):
                            trace.x = trace.x.tolist()
                        if hasattr(trace, 'y') and hasattr(trace.y, 'tolist'):
                            trace.y = trace.y.tolist()

                    # Use to_json with engine parameter
                    fig_json = fig.to_json(engine='json')


                except Exception as e2:

                    fig_json = fig.to_json()



            # Cache the chart data
            cache.set(id=id, field='chart', value=fig_json)
            cache.set(id=id, field='chart_code', value=plotly_code)
            cache.set(id=id, field='chat_history', value=vn.chat_history.copy())

            return {
                "type": "plotly_figure",
                "id": id,
                "fig": fig_json,
                "plotly_code": plotly_code
            }

        except Exception as e:
            logger.error(f"Error executing plotly code: {str(e)}")
            return {
                "type": "error",
                "id": id,
                "error": f"Failed to generate chart: {str(e)}",
                "plotly_code": plotly_code
            }

    except Exception as e:
        traceback.print_exc()
        raise HTTPException(status_code=500, detail={"type": "error", "error": str(e)})

