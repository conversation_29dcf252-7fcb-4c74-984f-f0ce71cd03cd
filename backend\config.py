import os
from dotenv import load_dotenv

load_dotenv()

class Config:
    SECRET_KEY = os.environ.get('SECRET_KEY', 'dev_key_change_in_production')
    DEBUG = os.environ.get('DEBUG', 'False').lower() == 'true'
    USE_RELOADER = os.environ.get('USE_RELOADER', 'False').lower() == 'true'

    # Hospital Database configuration (for SQL queries)
    HOSPITAL_DB_HOST = os.environ.get('HOSPITAL_DB_HOST', 'aws-0-ap-southeast-1.pooler.supabase.com')
    HOSPITAL_DB_NAME = os.environ.get('HOSPITAL_DB_NAME', 'postgres')
    HOSPITAL_DB_USER = os.environ.get('HOSPITAL_DB_USER', 'postgres.ixajpflnkexezmsxyofd')
    HOSPITAL_DB_PASSWORD = os.environ.get('HOSPITAL_DB_PASSWORD', '123456')
    if not HOSPITAL_DB_PASSWORD:
        raise ValueError("HOSPITAL_DB_PASSWORD environment variable must be set")
    try:
        HOSPITAL_DB_PORT = int(os.environ.get('HOSPITAL_DB_PORT', 5432))
        if not (1 <= HOSPITAL_DB_PORT <= 65535):
            raise ValueError(f"Invalid hospital database port number: {HOSPITAL_DB_PORT}")
    except ValueError as e:
        raise ValueError(f"Invalid HOSPITAL_DB_PORT value: {e}")

    # App Database configuration (for authentication and app data)
    APP_DB_HOST = os.environ.get('APP_DB_HOST', 'aws-0-ap-southeast-1.pooler.supabase.com')
    APP_DB_NAME = os.environ.get('APP_DB_NAME', 'postgres')
    APP_DB_USER = os.environ.get('APP_DB_USER', 'postgres.oizrmaqixykhjftegmtv')
    APP_DB_PASSWORD = os.environ.get('APP_DB_PASSWORD', '123456')
    if not APP_DB_PASSWORD:
        raise ValueError("APP_DB_PASSWORD environment variable must be set")
    try:
        APP_DB_PORT = int(os.environ.get('APP_DB_PORT', 6543))
        if not (1 <= APP_DB_PORT <= 65535):
            raise ValueError(f"Invalid app database port number: {APP_DB_PORT}")
    except ValueError as e:
        raise ValueError(f"Invalid APP_DB_PORT value: {e}")

    # Legacy database configuration (for backward compatibility)
    DB_HOST = HOSPITAL_DB_HOST
    DB_NAME = HOSPITAL_DB_NAME
    DB_USER = HOSPITAL_DB_USER
    DB_PASSWORD = HOSPITAL_DB_PASSWORD
    DB_PORT = HOSPITAL_DB_PORT
    
    # Ollama configuration
    OLLAMA_MODEL = os.environ.get('OLLAMA_MODEL', 'sam860/qwen3:4b-Q4_K_XL')
    OLLAMA_HOST = os.environ.get('OLLAMA_HOST', '127.0.0.1')

    # Ollama port configuration with validation
    try:
        OLLAMA_PORT = int(os.environ.get('OLLAMA_PORT', 11434))
        if not (1 <= OLLAMA_PORT <= 65535):
            raise ValueError(f"Invalid Ollama port number: {OLLAMA_PORT}")
    except ValueError as e:
        raise ValueError(f"Invalid OLLAMA_PORT value: {e}")

    # Construct full Ollama URL
    # Use HTTPS for ngrok tunnels on port 443, HTTP for local/other setups
    protocol = "https" if OLLAMA_PORT == 443 or "ngrok" in OLLAMA_HOST else "http"
    OLLAMA_URL = f"{protocol}://{OLLAMA_HOST}:{OLLAMA_PORT}"
    print(f"Connecting to Ollama at {OLLAMA_URL}")
    
    # Cache configuration
    CACHE_MAX_SIZE = 150
    CACHE_EXPIRATION_SECONDS = 7200  # 2 hours
    CACHE_DIR = os.environ.get('CACHE_DIR', 'persistent_cache')
    
    # SQL execution limits
    MAX_SQL_ROWS = 1000
    MAX_DF_SIZE_MB = 50
    SAMPLE_DF_SIZE_MB = 10

    # Dataframe context configuration
    USE_FULL_DATAFRAME_CONTEXT = os.environ.get('USE_FULL_DATAFRAME_CONTEXT', 'true').lower() == 'true'
    MAX_CONTEXT_ROWS = int(os.environ.get('MAX_CONTEXT_ROWS', '1000'))  # Maximum rows to include in model context
    MAX_CONTEXT_COLUMNS = int(os.environ.get('MAX_CONTEXT_COLUMNS', '50'))  # Maximum columns to include in model context
    
    @property
    def database_config(self):
        """Hospital database configuration (for backward compatibility)"""
        return {
            'host': self.HOSPITAL_DB_HOST,
            'dbname': self.HOSPITAL_DB_NAME,
            'user': self.HOSPITAL_DB_USER,
            'password': self.HOSPITAL_DB_PASSWORD,
            'port': self.HOSPITAL_DB_PORT
        }

    @property
    def hospital_database_config(self):
        """Hospital database configuration for SQL queries"""
        return {
            'host': self.HOSPITAL_DB_HOST,
            'dbname': self.HOSPITAL_DB_NAME,
            'user': self.HOSPITAL_DB_USER,
            'password': self.HOSPITAL_DB_PASSWORD,
            'port': self.HOSPITAL_DB_PORT
        }

    @property
    def app_database_config(self):
        """App database configuration for authentication and app data"""
        return {
            'host': self.APP_DB_HOST,
            'dbname': self.APP_DB_NAME,
            'user': self.APP_DB_USER,
            'password': self.APP_DB_PASSWORD,
            'port': self.APP_DB_PORT
        }

    @property
    def hospital_database_dsn(self):
        """Hospital database DSN string"""
        config = self.hospital_database_config
        return f"postgresql://{config['user']}:{config['password']}@{config['host']}:{config['port']}/{config['dbname']}"

    @property
    def app_database_dsn(self):
        """App database DSN string"""
        config = self.app_database_config
        return f"postgresql://{config['user']}:{config['password']}@{config['host']}:{config['port']}/{config['dbname']}"
    
    @property
    def vanna_config(self):
        # Get absolute path to backend directory and create chromadb path
        backend_dir = os.path.dirname(os.path.abspath(__file__))
        chromadb_path = os.path.join(backend_dir, 'chromadb')

        return {
            'model': self.OLLAMA_MODEL,
            'dialect': 'PostgreSQL',
            'host': self.OLLAMA_HOST,  # Just the host without protocol
            'port': self.OLLAMA_PORT,  # Separate port
            'ollama_host': self.OLLAMA_HOST,  # Vanna Ollama integration expects this key
            'max_tokens': 32000,
            'path': chromadb_path  # Absolute path for ChromaDB storage
        }

    @property
    def ollama_config(self):
        """Ollama configuration for direct client usage"""
        return {
            'model': self.OLLAMA_MODEL,
            'host': self.OLLAMA_HOST,
            'port': self.OLLAMA_PORT,
            'url': self.OLLAMA_URL
        }
