#!/usr/bin/env python3
"""
Make Migration Scripts Executable
=================================

This script makes the migration scripts executable on Unix-like systems.
"""

import os
import stat

def make_executable(file_path):
    """Make a file executable"""
    try:
        # Get current permissions
        current_permissions = os.stat(file_path).st_mode
        
        # Add execute permissions for owner, group, and others
        new_permissions = current_permissions | stat.S_IXUSR | stat.S_IXGRP | stat.S_IXOTH
        
        # Set new permissions
        os.chmod(file_path, new_permissions)
        print(f"✅ Made {file_path} executable")
        return True
        
    except Exception as e:
        print(f"❌ Failed to make {file_path} executable: {e}")
        return False

def main():
    """Make all migration scripts executable"""
    script_dir = os.path.dirname(__file__)
    
    scripts = [
        'database_purge_and_migrate.py',
        'run_migration.py',
        'backup_database.py'
    ]
    
    print("Making migration scripts executable...")
    
    for script in scripts:
        script_path = os.path.join(script_dir, script)
        if os.path.exists(script_path):
            make_executable(script_path)
        else:
            print(f"⚠️  Script not found: {script_path}")
    
    print("Done!")

if __name__ == "__main__":
    main()
