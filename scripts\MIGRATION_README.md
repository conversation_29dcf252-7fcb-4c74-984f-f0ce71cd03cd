# Database Migration Scripts

This directory contains scripts to purge and recreate the app database with the new linear chat architecture and analytics enhancements.

## ⚠️ Important Warning

**These scripts will COMPLETELY PURGE your existing app database and recreate it from scratch. ALL EXISTING DATA WILL BE LOST!**

## Scripts Overview

### 1. `database_purge_and_migrate.py`
Main migration script that:
- Purges all existing tables and data
- Recreates schema from `sql/create_app_db.sql`
- Adds linear chat architecture enhancements
- Creates analytics tables for the new dashboard
- Seeds initial data (departments, roles, permissions, topics)
- Sets up migration tracking

### 2. `run_migration.py`
Simple runner script for automated deployments (no interactive prompts).

### 3. `backup_database.py`
Creates a backup of the current database before migration (optional but recommended).

## Migration Process

### Step 1: Backup Current Database (Recommended)
```bash
cd scripts
python backup_database.py
```

### Step 2: Run Migration
Choose one of these options:

**Interactive Mode (with safety confirmation):**
```bash
python database_purge_and_migrate.py
```

**Automated Mode (no prompts):**
```bash
python run_migration.py
```

## What the Migration Does

### Database Cleanup
- Drops all existing tables with CASCADE
- Removes all sequences
- Cleans up any duplicate/conflicting structures

### Schema Recreation
- Creates clean schema from `sql/create_app_db.sql`
- Removes duplicate tables (`chat_session`, `chat_message`, `chat_context`)
- Keeps only the new structure (`conversation`, `chat_history`)

### Linear Chat Architecture Enhancements
- Adds `title` and `metadata` columns to `conversation` table
- Creates unique constraint to prevent response branching
- Adds comprehensive comments explaining linear flow
- Optimizes indexes for conversation management

### Analytics Dashboard Tables
- `user_engagement_metrics`: Daily user activity metrics
- `conversation_summary`: AI-generated conversation summaries
- Enhanced indexing for analytics queries

### Initial Data Seeding
- Hospital-specific roles (admin, head_of_department, medical_staff)
- Medical-focused topics for patient data retrieval
- No default departments (will be synced from hospital database)
- No permissions (RBAC not implemented yet)

## Database Schema Changes

### Removed Tables (Duplicates)
- `chat_session` → Replaced by `conversation`
- `chat_message` → Replaced by `chat_history`
- `chat_context` → Functionality moved to `chat_history.metadata`

### Enhanced Tables
- `conversation`: Added `title`, `metadata` columns
- `chat_history`: Enhanced with linear flow constraints

### New Tables
- `user_engagement_metrics`: For analytics dashboard
- `conversation_summary`: For AI-generated summaries
- `migrations`: For tracking applied migrations

## Complete Workflow

### Step 1: Backup (Optional but Recommended)
```bash
python scripts/backup_database.py
```

### Step 2: Run Migration
```bash
python scripts/database_purge_and_migrate.py
```

### Step 3: Sync Hospital Data
```bash
python scripts/sync_hospital_data.py
```

### Step 4: Verify Everything
```bash
python scripts/verify_migration.py
python scripts/test_migration_and_sync.py
```

## Post-Migration Steps

1. **Restart your application** to use the new schema
2. **Test chat functionality** to ensure linear flow works
3. **Verify analytics dashboard** can access new metrics tables
4. **Change default passwords** for security:
   - Admin user: `admin` / `admin123`
   - Medical staff: `doctor123`

## Troubleshooting

### Connection Issues
- Verify credentials in `backend/config.py`
- Ensure database is accessible
- Check firewall/network settings

### Permission Issues
- Ensure database user has CREATE/DROP privileges
- Check if user can create/drop tables and sequences

### Migration Failures
- Check the generated log file for detailed error messages
- Verify SQL syntax in `sql/create_app_db.sql`
- Ensure no active connections to the database during migration

## Rollback

If you need to rollback:
1. Use the backup created by `backup_database.py`
2. Restore using `psql` or your preferred database tool:
   ```bash
   psql -h host -p port -U user -d database -f backup_file.sql
   ```

## Configuration

The scripts use database credentials from `backend/config.py`:
- `APP_DB_HOST`
- `APP_DB_NAME` 
- `APP_DB_USER`
- `APP_DB_PASSWORD`
- `APP_DB_PORT`

Make sure these are correctly configured before running the migration.

## Logging

All migration activities are logged to:
- Console output (real-time)
- Log file: `database_migration_YYYYMMDD_HHMMSS.log`

## Support

If you encounter issues:
1. Check the log files for detailed error messages
2. Verify your database configuration
3. Ensure you have the necessary database privileges
4. Review the SQL files for syntax errors
