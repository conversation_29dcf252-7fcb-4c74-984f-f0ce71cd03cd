"""
API Versioning Middleware and Utilities

This module provides middleware and utilities for API versioning support.
"""

import logging
from typing import Callable
from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware

logger = logging.getLogger(__name__)


class APIVersionMiddleware(BaseHTTPMiddleware):
    """
    Middleware to add API version headers to all responses
    """

    def __init__(self, app, api_version: str = "2.0.0"):
        super().__init__(app)
        self.api_version = api_version

    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """
        Add version headers to all API responses
        """
        response = await call_next(request)

        # Add API version headers
        response.headers["X-API-Version"] = self.api_version

        # All endpoints should use v1 prefix
        path = request.url.path
        if path.startswith("/api/v1/"):
            response.headers["X-Endpoint-Version"] = "v1"
            response.headers["X-Version-Status"] = "stable"
        else:
            response.headers["X-Endpoint-Version"] = "unknown"
            response.headers["X-Version-Status"] = "unknown"
            # Add warning for non-v1 endpoints
            response.headers["X-Migration-Warning"] = "All endpoints should use /api/v1/ prefix"

        return response


def get_version_from_path(path: str) -> dict:
    """
    Extract version information from request path

    Args:
        path: Request path

    Returns:
        Dictionary with version information
    """
    if path.startswith("/api/v1/"):
        return {
            "version": "v1",
            "status": "stable",
            "base_path": "/api/v1",
            "is_legacy": False
        }
    else:
        return {
            "version": "unknown",
            "status": "unknown",
            "base_path": "",
            "is_legacy": False
        }


def add_version_info_to_response(response_data: dict, request: Request) -> dict:
    """
    Add version information to API response data

    Args:
        response_data: Original response data
        request: FastAPI request object

    Returns:
        Response data with version information added
    """
    version_info = get_version_from_path(request.url.path)

    # Add version metadata to response
    if isinstance(response_data, dict):
        response_data["_meta"] = {
            "api_version": "2.0.0",
            "endpoint_version": version_info["version"],
            "version_status": version_info["status"],
            "is_legacy": version_info["is_legacy"],
            "timestamp": request.state.__dict__.get("request_timestamp")
        }

    return response_data


class VersionedResponse:
    """
    Utility class for creating versioned API responses
    """
    
    @staticmethod
    def success(data: dict, request: Request, message: str = None) -> dict:
        """Create a successful versioned response"""
        response = {
            "status": "success",
            "data": data
        }
        
        if message:
            response["message"] = message
            
        return add_version_info_to_response(response, request)
    
    @staticmethod
    def error(error: str, request: Request, code: str = None) -> dict:
        """Create an error versioned response"""
        response = {
            "status": "error",
            "error": error
        }
        
        if code:
            response["error_code"] = code
            
        return add_version_info_to_response(response, request)
    
    @staticmethod
    def paginated(data: list, request: Request, total: int = None, page: int = 1, per_page: int = 50) -> dict:
        """Create a paginated versioned response"""
        response = {
            "status": "success",
            "data": data,
            "pagination": {
                "page": page,
                "per_page": per_page,
                "total": total or len(data),
                "has_next": total and (page * per_page) < total if total else False,
                "has_prev": page > 1
            }
        }
        
        return add_version_info_to_response(response, request)
