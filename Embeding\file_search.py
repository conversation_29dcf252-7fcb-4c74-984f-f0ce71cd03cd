"""
File-Specific Medical Record Search

This tool helps you find exactly which file contains specific content,
without losing any information due to deduplication.
"""

import os
import sys
from pathlib import Path
from typing import List, Dict
import json

# Add the current directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from medical_record_embedder import MedicalRecordEmbedder
from config import *

class FileSpecificSearch:
    """Search tool that preserves file identity and location"""
    
    def __init__(self):
        self.embedder = MedicalRecordEmbedder(model_name=EMBEDDING_MODEL)
        self.all_records = None
        
    def load_all_records(self):
        """Load all records without any deduplication"""
        if self.all_records is None:
            print("📚 Loading all medical records (no deduplication)...")
            self.all_records = []
            
            dataset_path = Path(DATASET_PATH)
            md_files = list(dataset_path.glob("*.md"))
            
            for file_path in md_files:
                record = self.embedder.parse_medical_record(str(file_path))
                if record:
                    self.all_records.append(record)
            
            print(f"✅ Loaded {len(self.all_records)} records from {len(md_files)} files")
        
        return self.all_records
    
    def find_exact_content(self, search_text: str, case_sensitive: bool = False) -> List[Dict]:
        """
        Find exact content in files
        
        Args:
            search_text: Text to search for
            case_sensitive: Whether to match case exactly
            
        Returns:
            List of files containing the exact text
        """
        records = self.load_all_records()
        matches = []
        
        for record in records:
            # Read the original file content
            try:
                with open(record.file_path, 'r', encoding='utf-8') as f:
                    file_content = f.read()
                
                # Search logic
                if case_sensitive:
                    found = search_text in file_content
                    search_positions = []
                    start = 0
                    while True:
                        pos = file_content.find(search_text, start)
                        if pos == -1:
                            break
                        search_positions.append(pos)
                        start = pos + 1
                else:
                    found = search_text.lower() in file_content.lower()
                    search_positions = []
                    start = 0
                    file_lower = file_content.lower()
                    search_lower = search_text.lower()
                    while True:
                        pos = file_lower.find(search_lower, start)
                        if pos == -1:
                            break
                        search_positions.append(pos)
                        start = pos + 1
                
                if found:
                    # Extract context around each match
                    contexts = []
                    for pos in search_positions:
                        context_start = max(0, pos - 150)
                        context_end = min(len(file_content), pos + len(search_text) + 150)
                        context = file_content[context_start:context_end]
                        
                        contexts.append({
                            'position': pos,
                            'context': context,
                            'line_number': file_content[:pos].count('\n') + 1
                        })
                    
                    matches.append({
                        'file_path': record.file_path,
                        'file_name': Path(record.file_path).name,
                        'patient_id': record.patient_id,
                        'record_type': record.record_type,
                        'primary_diagnosis': record.primary_diagnosis,
                        'match_count': len(search_positions),
                        'contexts': contexts
                    })
                    
            except Exception as e:
                print(f"Error reading {record.file_path}: {e}")
        
        return matches
    
    def search_by_semantic_similarity(self, query: str, include_file_names: bool = True) -> List[Dict]:
        """
        Search by semantic similarity while preserving file information
        
        Args:
            query: Search query
            include_file_names: Whether to include file names in results
            
        Returns:
            Search results with file information
        """
        try:
            # Setup Qdrant if not already done
            self.embedder.setup_qdrant(host=QDRANT_HOST, port=QDRANT_PORT)
            
            # Get semantic search results
            results = self.embedder.query_similar_records(query, limit=20)
            
            # Enhance results with file-specific information
            enhanced_results = []
            for result in results:
                enhanced_result = result.copy()
                file_path = result['record']['file_path']
                enhanced_result['file_name'] = Path(file_path).name
                
                # Add file size and modification time
                try:
                    file_stat = Path(file_path).stat()
                    enhanced_result['file_size'] = file_stat.st_size
                    enhanced_result['last_modified'] = file_stat.st_mtime
                except:
                    pass
                
                enhanced_results.append(enhanced_result)
            
            return enhanced_results
            
        except Exception as e:
            print(f"Error in semantic search: {e}")
            return []
    
    def compare_files(self, file1_path: str, file2_path: str) -> Dict:
        """
        Compare content between two specific files
        
        Args:
            file1_path: Path to first file
            file2_path: Path to second file
            
        Returns:
            Comparison results
        """
        try:
            record1 = self.embedder.parse_medical_record(file1_path)
            record2 = self.embedder.parse_medical_record(file2_path)
            
            if not record1 or not record2:
                return {'error': 'Could not parse one or both files'}
            
            # Calculate content similarity
            similarity = self.embedder._calculate_similarity(
                record1.specialized_content,
                record2.specialized_content
            )
            
            # Compare specific fields
            comparison = {
                'files': {
                    'file1': Path(file1_path).name,
                    'file2': Path(file2_path).name
                },
                'similarity_score': similarity,
                'content_hash_match': record1.content_hash == record2.content_hash,
                'field_comparisons': {
                    'patient_id': {
                        'file1': record1.patient_id,
                        'file2': record2.patient_id,
                        'match': record1.patient_id == record2.patient_id
                    },
                    'primary_diagnosis': {
                        'file1': record1.primary_diagnosis,
                        'file2': record2.primary_diagnosis,
                        'match': record1.primary_diagnosis == record2.primary_diagnosis
                    },
                    'record_type': {
                        'file1': record1.record_type,
                        'file2': record2.record_type,
                        'match': record1.record_type == record2.record_type
                    },
                    'medications': {
                        'file1': record1.medications,
                        'file2': record2.medications,
                        'common': list(set(record1.medications) & set(record2.medications)),
                        'unique_to_file1': list(set(record1.medications) - set(record2.medications)),
                        'unique_to_file2': list(set(record2.medications) - set(record1.medications))
                    }
                }
            }
            
            return comparison
            
        except Exception as e:
            return {'error': f'Error comparing files: {e}'}
    
    def find_similar_files_to_target(self, target_file: str, similarity_threshold: float = 0.3) -> List[Dict]:
        """
        Find files similar to a target file
        
        Args:
            target_file: Path to the target file
            similarity_threshold: Minimum similarity score
            
        Returns:
            List of similar files with similarity scores
        """
        records = self.load_all_records()
        target_record = None
        
        # Find the target record
        for record in records:
            if record.file_path == target_file or Path(record.file_path).name == Path(target_file).name:
                target_record = record
                break
        
        if not target_record:
            return [{'error': f'Target file not found: {target_file}'}]
        
        similar_files = []
        
        for record in records:
            if record.file_path == target_record.file_path:
                continue  # Skip the target file itself
            
            similarity = self.embedder._calculate_similarity(
                target_record.specialized_content,
                record.specialized_content
            )
            
            if similarity >= similarity_threshold:
                similar_files.append({
                    'file_path': record.file_path,
                    'file_name': Path(record.file_path).name,
                    'similarity_score': similarity,
                    'patient_id': record.patient_id,
                    'record_type': record.record_type,
                    'primary_diagnosis': record.primary_diagnosis,
                    'is_exact_duplicate': target_record.content_hash == record.content_hash
                })
        
        # Sort by similarity score
        similar_files.sort(key=lambda x: x['similarity_score'], reverse=True)
        
        return similar_files
    
    def analyze_all_files(self) -> Dict:
        """
        Analyze all files for duplicates and similarities
        
        Returns:
            Complete analysis of the dataset
        """
        records = self.load_all_records()
        analysis = self.embedder.analyze_record_similarity(records, similarity_threshold=0.7)
        
        # Add file-specific information
        analysis['file_details'] = []
        for record in records:
            try:
                file_stat = Path(record.file_path).stat()
                analysis['file_details'].append({
                    'file_name': Path(record.file_path).name,
                    'file_path': record.file_path,
                    'patient_id': record.patient_id,
                    'record_type': record.record_type,
                    'content_hash': record.content_hash[:8],
                    'file_size': file_stat.st_size,
                    'specialized_content_length': len(record.specialized_content)
                })
            except:
                pass
        
        return analysis

def main():
    """Main function for file-specific search"""
    if len(sys.argv) < 2:
        print("🔍 FILE-SPECIFIC MEDICAL RECORD SEARCH")
        print("=" * 50)
        print("Usage examples:")
        print("  python file_search.py find 'chest pain'")
        print("  python file_search.py semantic 'heart attack patient'")
        print("  python file_search.py compare data_1.md data_2.md")
        print("  python file_search.py similar data_1.md")
        print("  python file_search.py analyze")
        return
    
    search_tool = FileSpecificSearch()
    command = sys.argv[1].lower()
    
    if command == 'find' and len(sys.argv) > 2:
        # Find exact content
        search_text = ' '.join(sys.argv[2:])
        matches = search_tool.find_exact_content(search_text)
        
        print(f"🔍 Searching for exact text: '{search_text}'")
        print(f"📋 Found {len(matches)} files containing this text:")
        
        for match in matches:
            print(f"\n📄 File: {match['file_name']}")
            print(f"   Patient: {match['patient_id']}")
            print(f"   Type: {match['record_type']}")
            print(f"   Matches: {match['match_count']}")
            
            for i, context in enumerate(match['contexts'][:2], 1):  # Show first 2 contexts
                print(f"   Context {i} (line {context['line_number']}):")
                print(f"   '{context['context'][:100]}...'")
    
    elif command == 'semantic' and len(sys.argv) > 2:
        # Semantic search
        query = ' '.join(sys.argv[2:])
        results = search_tool.search_by_semantic_similarity(query)
        
        print(f"🔍 Semantic search for: '{query}'")
        print(f"📋 Found {len(results)} relevant files:")
        
        for result in results[:10]:  # Show top 10
            print(f"\n📄 File: {result['file_name']}")
            print(f"   Score: {result['score']:.3f}")
            print(f"   Patient: {result['record']['patient_id']}")
            print(f"   Diagnosis: {result['record']['primary_diagnosis']}")
    
    elif command == 'compare' and len(sys.argv) > 3:
        # Compare two files
        file1 = sys.argv[2]
        file2 = sys.argv[3]
        
        # Add dataset path if not absolute
        if not Path(file1).is_absolute():
            file1 = str(Path(DATASET_PATH) / file1)
        if not Path(file2).is_absolute():
            file2 = str(Path(DATASET_PATH) / file2)
        
        comparison = search_tool.compare_files(file1, file2)
        
        if 'error' in comparison:
            print(f"❌ {comparison['error']}")
        else:
            print(f"📊 Comparing {comparison['files']['file1']} vs {comparison['files']['file2']}")
            print(f"Similarity Score: {comparison['similarity_score']:.3f}")
            print(f"Exact Duplicate: {comparison['content_hash_match']}")
            
            print("\nField Comparisons:")
            for field, data in comparison['field_comparisons'].items():
                if field == 'medications':
                    print(f"  {field}:")
                    print(f"    Common: {data['common']}")
                    print(f"    Unique to file1: {data['unique_to_file1']}")
                    print(f"    Unique to file2: {data['unique_to_file2']}")
                else:
                    match_status = "✅ Match" if data['match'] else "❌ Different"
                    print(f"  {field}: {match_status}")
    
    elif command == 'similar' and len(sys.argv) > 2:
        # Find files similar to target
        target_file = sys.argv[2]
        
        if not Path(target_file).is_absolute():
            target_file = str(Path(DATASET_PATH) / target_file)
        
        similar_files = search_tool.find_similar_files_to_target(target_file)
        
        if similar_files and 'error' in similar_files[0]:
            print(f"❌ {similar_files[0]['error']}")
        else:
            print(f"🔍 Files similar to: {Path(target_file).name}")
            print(f"📋 Found {len(similar_files)} similar files:")
            
            for similar in similar_files:
                duplicate_marker = " [EXACT DUPLICATE]" if similar['is_exact_duplicate'] else ""
                print(f"\n📄 {similar['file_name']}{duplicate_marker}")
                print(f"   Similarity: {similar['similarity_score']:.3f}")
                print(f"   Patient: {similar['patient_id']}")
                print(f"   Type: {similar['record_type']}")
    
    elif command == 'analyze':
        # Analyze all files
        analysis = search_tool.analyze_all_files()
        print("📊 COMPLETE FILE ANALYSIS")
        print("=" * 30)
        print(f"Total files: {analysis['total_records']}")
        print(f"Unique content hashes: {analysis['unique_records']}")
        print(f"Exact duplicate groups: {len(analysis['exact_duplicates'])}")
        print(f"Similar (not identical) groups: {len(analysis['similar_groups'])}")
        
        if analysis['exact_duplicates']:
            print("\nExact Duplicates:")
            for hash_short, files in analysis['exact_duplicates'].items():
                print(f"  Hash {hash_short}: {[Path(f).name for f in files]}")
    
    else:
        print("❌ Invalid command or missing arguments")
        print("Use: python file_search.py [find|semantic|compare|similar|analyze] [arguments]")

if __name__ == "__main__":
    main()
